<?php

Route::redirect('/', '/login');
Route::get('/home', function () {
    if (session('status')) {
        return redirect()->route('admin.home')->with('status', session('status'));
    }

    return redirect()->route('admin.home');
});

Auth::routes();

Route::get('change-language/{change_language}', function ($change_language) {
    session()->put('language', $change_language);
    return redirect()->back();
})->name('change-language');

Route::group(['prefix' => 'admin', 'as' => 'admin.', 'namespace' => 'Admin', 'middleware' => ['auth', '2fa']], function () {
    Route::get('/', 'HomeController@index')->name('home');

    // Customer Address routes
    Route::group(['prefix' => 'customer-addresses'], function () {
        Route::post('/create-form', 'CustomerAddressController@createForm')->name('customer-addresses.create-form');
        Route::post('/edit-form', 'CustomerAddressController@editForm')->name('customer-addresses.edit-form');
        Route::post('/store', 'CustomerAddressController@store')->name('customer-addresses.store');
        Route::put('/{address}', 'CustomerAddressController@update')->name('customer-addresses.update');
        Route::delete('/{address}', 'CustomerAddressController@destroy')->name('customer-addresses.destroy');
    });

    // Permissions
    Route::delete('permissions/destroy', 'PermissionsController@massDestroy')->name('permissions.massDestroy');
    Route::post('permissions/parse-csv-import', 'PermissionsController@parseCsvImport')->name('permissions.parseCsvImport');
    Route::post('permissions/process-csv-import', 'PermissionsController@processCsvImport')->name('permissions.processCsvImport');
    Route::resource('permissions', 'PermissionsController');

    // Roles
    Route::delete('roles/destroy', 'RolesController@massDestroy')->name('roles.massDestroy');
    Route::post('roles/parse-csv-import', 'RolesController@parseCsvImport')->name('roles.parseCsvImport');
    Route::post('roles/process-csv-import', 'RolesController@processCsvImport')->name('roles.processCsvImport');
    Route::resource('roles', 'RolesController');

    // Users
    Route::delete('users/destroy', 'UsersController@massDestroy')->name('users.massDestroy');
    Route::post('users/parse-csv-import', 'UsersController@parseCsvImport')->name('users.parseCsvImport');
    Route::post('users/process-csv-import', 'UsersController@processCsvImport')->name('users.processCsvImport');
    Route::resource('users', 'UsersController');

    // Audit Logs
    Route::resource('audit-logs', 'AuditLogsController', ['except' => ['create', 'store', 'edit', 'update', 'destroy']]);

    // Team
    Route::delete('teams/destroy', 'TeamController@massDestroy')->name('teams.massDestroy');
    Route::post('teams/parse-csv-import', 'TeamController@parseCsvImport')->name('teams.parseCsvImport');
    Route::post('teams/process-csv-import', 'TeamController@processCsvImport')->name('teams.processCsvImport');
    Route::resource('teams', 'TeamController');

    // Provider
    Route::delete('providers/destroy', 'ProviderController@massDestroy')->name('providers.massDestroy');
    Route::post('providers/media', 'ProviderController@storeMedia')->name('providers.storeMedia');
    Route::post('providers/ckmedia', 'ProviderController@storeCKEditorImages')->name('providers.storeCKEditorImages');
    Route::post('providers/parse-csv-import', 'ProviderController@parseCsvImport')->name('providers.parseCsvImport');
    Route::post('providers/process-csv-import', 'ProviderController@processCsvImport')->name('providers.processCsvImport');

    Route::get('providers/location/{provider}', 'ProviderController@providerLocation')->name('providers.location');
    Route::post('providers/location/add/form', 'ProviderController@locationAddForm')->name('providers.locationAddForm');
    Route::post('providers/location/edit/form', 'ProviderController@locationEditForm')->name('providers.locationEditForm');

    Route::get('providers/age-group/{provider}', 'ProviderController@providerAgeGroup')->name('providers.ageGroup');
    Route::post('providers/age-group/edit/form', 'ProviderController@ageGroupEditForm')->name('providers.ageGroupEditForm');
    Route::get('providers/social/{provider}', 'ProviderController@providerSocial')->name('providers.social');
    Route::get('providers/media-library/{provider}', 'ProviderController@mediaLibrary')->name('providers.mediaLibrary');
    Route::post('providers/media-library/{provider}/upload', 'ProviderController@mediaLibraryUpload')->name('providers.mediaLibraryUpload');

    Route::get('providers/packages/{provider}', 'ProviderController@providerPackages')->name('providers.packages');
    Route::get('providers/banks/{provider}', 'ProviderController@providerBanks')->name('providers.banks');
    Route::post('providers/banks/add/form', 'ProviderController@bankAddForm')->name('providers.bankAddForm');
    Route::post('providers/banks/edit/form', 'ProviderController@bankEditForm')->name('providers.bankEditForm');

    Route::get('providers/billing/{provider}', 'ProviderController@providerBilling')->name('providers.billing');
    Route::post('providers/billing/{provider}', 'ProviderController@updateBilling')->name('providers.updateBilling');


    // Route::get('providers/detail/{provider}', 'ProviderController@detail')->name('providers.detail');
    // Route::get('providers/slider/{provider}', 'ProviderController@slider')->name('providers.slider');
    // Route::get('providers/{provider}/locations', 'ProviderController@providerLocation')->name('providers.providerLocation');

    Route::resource('providers', 'ProviderController');

    // Provider Catergory
    Route::delete('provider-catergories/destroy', 'ProviderCatergoryController@massDestroy')->name('provider-catergories.massDestroy');
    Route::post('provider-catergories/parse-csv-import', 'ProviderCatergoryController@parseCsvImport')->name('provider-catergories.parseCsvImport');
    Route::post('provider-catergories/process-csv-import', 'ProviderCatergoryController@processCsvImport')->name('provider-catergories.processCsvImport');
    Route::get('provider-catergories/form', 'ProviderCatergoryController@getForm')->name('provider-catergories.getForm');

    Route::resource('provider-catergories', 'ProviderCatergoryController');

    // Currency
    Route::delete('currencies/destroy', 'CurrencyController@massDestroy')->name('currencies.massDestroy');
    Route::post('currencies/parse-csv-import', 'CurrencyController@parseCsvImport')->name('currencies.parseCsvImport');
    Route::post('currencies/process-csv-import', 'CurrencyController@processCsvImport')->name('currencies.processCsvImport');
    Route::resource('currencies', 'CurrencyController');

    // Day Of Week
    Route::delete('day-of-weeks/destroy', 'DayOfWeekController@massDestroy')->name('day-of-weeks.massDestroy');
    Route::post('day-of-weeks/parse-csv-import', 'DayOfWeekController@parseCsvImport')->name('day-of-weeks.parseCsvImport');
    Route::post('day-of-weeks/process-csv-import', 'DayOfWeekController@processCsvImport')->name('day-of-weeks.processCsvImport');
    Route::resource('day-of-weeks', 'DayOfWeekController');

    // Season
    Route::delete('seasons/destroy', 'SeasonController@massDestroy')->name('seasons.massDestroy');
    Route::post('seasons/media', 'SeasonController@storeMedia')->name('seasons.storeMedia');
    Route::post('seasons/ckmedia', 'SeasonController@storeCKEditorImages')->name('seasons.storeCKEditorImages');
    Route::post('seasons/parse-csv-import', 'SeasonController@parseCsvImport')->name('seasons.parseCsvImport');
    Route::post('seasons/process-csv-import', 'SeasonController@processCsvImport')->name('seasons.processCsvImport');
    Route::resource('seasons', 'SeasonController');

    // Age Group
    Route::delete('age-groups/destroy', 'AgeGroupController@massDestroy')->name('age-groups.massDestroy');
    Route::post('age-groups/media', 'AgeGroupController@storeMedia')->name('age-groups.storeMedia');
    Route::post('age-groups/ckmedia', 'AgeGroupController@storeCKEditorImages')->name('age-groups.storeCKEditorImages');
    Route::post('age-groups/parse-csv-import', 'AgeGroupController@parseCsvImport')->name('age-groups.parseCsvImport');
    Route::post('age-groups/process-csv-import', 'AgeGroupController@processCsvImport')->name('age-groups.processCsvImport');
    Route::resource('age-groups', 'AgeGroupController');

    // Package
    Route::delete('packages/destroy', 'PackageController@massDestroy')->name('packages.massDestroy');
    Route::post('packages/media', 'PackageController@storeMedia')->name('packages.storeMedia');
    Route::post('packages/ckmedia', 'PackageController@storeCKEditorImages')->name('packages.storeCKEditorImages');
    Route::post('packages/parse-csv-import', 'PackageController@parseCsvImport')->name('packages.parseCsvImport');
    Route::post('packages/process-csv-import', 'PackageController@processCsvImport')->name('packages.processCsvImport');

    Route::get('packages/location/{package}', 'PackageController@packageLocation')->name('packages.location');
    Route::post('packages/location/add/form', 'PackageController@locationAddForm')->name('packages.locationAddForm');
    Route::post('packages/location/edit/form', 'PackageController@locationEditForm')->name('packages.locationEditForm');

    Route::get('packages/schedule/{package}', 'PackageController@packageSchedule')->name('packages.schedule');
    Route::post('packages/schedule/edit/form', 'PackageController@scheduleEditForm')->name('packages.scheduleEditForm');

    Route::get('packages/price/{package}', 'PackageController@packagePrice')->name('packages.price');
    Route::post('packages/price/edit/form', 'PackageController@priceEditForm')->name('packages.priceEditForm');

    Route::get('packages/media-library/{package}', 'PackageController@mediaLibrary')->name('packages.mediaLibrary');
    Route::post('packages/media-library/{package}/upload', 'PackageController@mediaLibraryUpload')->name('packages.mediaLibraryUpload');

    Route::resource('packages', 'PackageController');

    // Package Schedule
    Route::delete('package-schedules/destroy', 'PackageScheduleController@massDestroy')->name('package-schedules.massDestroy');
    Route::post('package-schedules/parse-csv-import', 'PackageScheduleController@parseCsvImport')->name('package-schedules.parseCsvImport');
    Route::post('package-schedules/process-csv-import', 'PackageScheduleController@processCsvImport')->name('package-schedules.processCsvImport');
    Route::resource('package-schedules', 'PackageScheduleController');

    // Package Price
    Route::delete('package-prices/destroy', 'PackagePriceController@massDestroy')->name('package-prices.massDestroy');
    Route::post('package-prices/parse-csv-import', 'PackagePriceController@parseCsvImport')->name('package-prices.parseCsvImport');
    Route::post('package-prices/process-csv-import', 'PackagePriceController@processCsvImport')->name('package-prices.processCsvImport');
    Route::resource('package-prices', 'PackagePriceController');

    // Country
    Route::delete('countries/destroy', 'CountryController@massDestroy')->name('countries.massDestroy');
    Route::post('countries/parse-csv-import', 'CountryController@parseCsvImport')->name('countries.parseCsvImport');
    Route::post('countries/process-csv-import', 'CountryController@processCsvImport')->name('countries.processCsvImport');
    Route::get('countries/form', 'CountryController@getForm')->name('countries.getForm');
    Route::resource('countries', 'CountryController');

    // Package Location
    Route::delete('package-locations/destroy', 'PackageLocationController@massDestroy')->name('package-locations.massDestroy');
    Route::post('package-locations/parse-csv-import', 'PackageLocationController@parseCsvImport')->name('package-locations.parseCsvImport');
    Route::post('package-locations/process-csv-import', 'PackageLocationController@processCsvImport')->name('package-locations.processCsvImport');
    Route::resource('package-locations', 'PackageLocationController');

    // Provider Location
    Route::delete('provider-locations/destroy', 'ProviderLocationController@massDestroy')->name('provider-locations.massDestroy');
    Route::post('provider-locations/parse-csv-import', 'ProviderLocationController@parseCsvImport')->name('provider-locations.parseCsvImport');
    Route::post('provider-locations/process-csv-import', 'ProviderLocationController@processCsvImport')->name('provider-locations.processCsvImport');
    Route::resource('provider-locations', 'ProviderLocationController');

    // Customer
    Route::delete('customers/destroy', 'CustomerController@massDestroy')->name('customers.massDestroy');
    Route::post('customers/media', 'CustomerController@storeMedia')->name('customers.storeMedia');
    Route::post('customers/ckmedia', 'CustomerController@storeCKEditorImages')->name('customers.storeCKEditorImages');
    Route::post('customers/parse-csv-import', 'CustomerController@parseCsvImport')->name('customers.parseCsvImport');
    Route::post('customers/process-csv-import', 'CustomerController@processCsvImport')->name('customers.processCsvImport');
    Route::get('customers/form', 'CustomerController@getForm')->name('customers.getForm');

    // Add these routes before the resource route
    Route::get('customers/address/{customer}', 'CustomerController@customerAddress')->name('customers.address');
    Route::post('customers/address/add/form', 'CustomerController@addressAddForm')->name('customers.addressAddForm');
    Route::post('customers/address/edit/form', 'CustomerController@addressEditForm')->name('customers.addressEditForm');

    Route::get('customers/email/{customer}', 'CustomerController@customerEmail')->name('customers.email');
    Route::post('customers/email/add/form', 'CustomerController@emailAddForm')->name('customers.emailAddForm');
    Route::post('customers/email/edit/form', 'CustomerController@emailEditForm')->name('customers.emailEditForm');

    Route::get('customers/pass/{customer}', 'CustomerController@customerPass')->name('customers.pass');
    Route::post('customers/pass/add/form', 'CustomerController@passAddForm')->name('customers.passAddForm');
    Route::post('customers/pass/edit/form', 'CustomerController@passEditForm')->name('customers.passEditForm');

    Route::get('customers/detail/{customer}', 'CustomerController@customerDetail')->name('customers.detail');
    Route::post('customers/detail/add/form', 'CustomerController@detailAddForm')->name('customers.detailAddForm');
    Route::post('customers/detail/edit/form', 'CustomerController@detailEditForm')->name('customers.detailEditForm');

    Route::get('customers/groups/{customer}', 'CustomerController@customerGroups')->name('customers.groups');
    Route::post('customers/groups/add/form', 'CustomerController@detailAddForm')->name('customers.groupsAddForm');
    Route::post('customers/groups/edit/form', 'CustomerController@detailEditForm')->name('customers.groupsEditForm');

    Route::resource('customers', 'CustomerController');

    // Gender
    Route::delete('genders/destroy', 'GenderController@massDestroy')->name('genders.massDestroy');
    Route::post('genders/parse-csv-import', 'GenderController@parseCsvImport')->name('genders.parseCsvImport');
    Route::post('genders/process-csv-import', 'GenderController@processCsvImport')->name('genders.processCsvImport');
    Route::resource('genders', 'GenderController');

    // Provider Type
    Route::delete('provider-types/destroy', 'ProviderTypeController@massDestroy')->name('provider-types.massDestroy');
    Route::post('provider-types/parse-csv-import', 'ProviderTypeController@parseCsvImport')->name('provider-types.parseCsvImport');
    Route::post('provider-types/process-csv-import', 'ProviderTypeController@processCsvImport')->name('provider-types.processCsvImport');
    Route::get('provider-types/form', 'ProviderTypeController@getForm')->name('provider-types.getForm');
    Route::resource('provider-types', 'ProviderTypeController');

    // Package Type
    Route::delete('package-types/destroy', 'PackageTypeController@massDestroy')->name('package-types.massDestroy');
    Route::post('package-types/parse-csv-import', 'PackageTypeController@parseCsvImport')->name('package-types.parseCsvImport');
    Route::post('package-types/process-csv-import', 'PackageTypeController@processCsvImport')->name('package-types.processCsvImport');
    Route::get('package-types/form', 'PackageTypeController@getForm')->name('package-types.getForm');
    Route::resource('package-types', 'PackageTypeController');

    // Pass Add On
    Route::delete('pass-add-ons/destroy', 'PassAddOnController@massDestroy')->name('pass-add-ons.massDestroy');
    Route::post('pass-add-ons/media', 'PassAddOnController@storeMedia')->name('pass-add-ons.storeMedia');
    Route::post('pass-add-ons/ckmedia', 'PassAddOnController@storeCKEditorImages')->name('pass-add-ons.storeCKEditorImages');
    Route::post('pass-add-ons/parse-csv-import', 'PassAddOnController@parseCsvImport')->name('pass-add-ons.parseCsvImport');
    Route::post('pass-add-ons/process-csv-import', 'PassAddOnController@processCsvImport')->name('pass-add-ons.processCsvImport');
    Route::resource('pass-add-ons', 'PassAddOnController');

    // Pass Add On Type
    Route::delete('pass-add-on-types/destroy', 'PassAddOnTypeController@massDestroy')->name('pass-add-on-types.massDestroy');
    Route::post('pass-add-on-types/parse-csv-import', 'PassAddOnTypeController@parseCsvImport')->name('pass-add-on-types.parseCsvImport');
    Route::post('pass-add-on-types/process-csv-import', 'PassAddOnTypeController@processCsvImport')->name('pass-add-on-types.processCsvImport');
    Route::resource('pass-add-on-types', 'PassAddOnTypeController');

    // Customer Address
    Route::delete('customer-addresses/destroy', 'CustomerAddressController@massDestroy')->name('customer-addresses.massDestroy');
    Route::post('customer-addresses/parse-csv-import', 'CustomerAddressController@parseCsvImport')->name('customer-addresses.parseCsvImport');
    Route::post('customer-addresses/process-csv-import', 'CustomerAddressController@processCsvImport')->name('customer-addresses.processCsvImport');
    Route::resource('customer-addresses', 'CustomerAddressController');

    // Customer Email
    Route::delete('customer-emails/destroy', 'CustomerEmailController@massDestroy')->name('customer-emails.massDestroy');
    Route::post('customer-emails/parse-csv-import', 'CustomerEmailController@parseCsvImport')->name('customer-emails.parseCsvImport');
    Route::post('customer-emails/process-csv-import', 'CustomerEmailController@processCsvImport')->name('customer-emails.processCsvImport');
    Route::resource('customer-emails', 'CustomerEmailController');

    // Customer Detail
    Route::delete('customer-details/destroy', 'CustomerDetailController@massDestroy')->name('customer-details.massDestroy');
    Route::post('customer-details/parse-csv-import', 'CustomerDetailController@parseCsvImport')->name('customer-details.parseCsvImport');
    Route::post('customer-details/process-csv-import', 'CustomerDetailController@processCsvImport')->name('customer-details.processCsvImport');
    Route::resource('customer-details', 'CustomerDetailController');

    // Issuer Catergory
    Route::delete('issuer-catergories/destroy', 'IssuerCatergoryController@massDestroy')->name('issuer-catergories.massDestroy');
    Route::post('issuer-catergories/parse-csv-import', 'IssuerCatergoryController@parseCsvImport')->name('issuer-catergories.parseCsvImport');
    Route::post('issuer-catergories/process-csv-import', 'IssuerCatergoryController@processCsvImport')->name('issuer-catergories.processCsvImport');
    Route::get('issuer-catergories/form', 'IssuerCatergoryController@getForm')->name('issuer-catergories.getForm');
    Route::resource('issuer-catergories', 'IssuerCatergoryController');

    // Issuer Location
    Route::delete('issuer-locations/destroy', 'IssuerLocationController@massDestroy')->name('issuer-locations.massDestroy');
    Route::post('issuer-locations/parse-csv-import', 'IssuerLocationController@parseCsvImport')->name('issuer-locations.parseCsvImport');
    Route::post('issuer-locations/process-csv-import', 'IssuerLocationController@processCsvImport')->name('issuer-locations.processCsvImport');
    Route::resource('issuer-locations', 'IssuerLocationController');

    // Issuer
    Route::delete('issuers/destroy', 'IssuerController@massDestroy')->name('issuers.massDestroy');
    Route::post('issuers/media', 'IssuerController@storeMedia')->name('issuers.storeMedia');
    Route::post('issuers/ckmedia', 'IssuerController@storeCKEditorImages')->name('issuers.storeCKEditorImages');
    Route::post('issuers/parse-csv-import', 'IssuerController@parseCsvImport')->name('issuers.parseCsvImport');
    Route::post('issuers/process-csv-import', 'IssuerController@processCsvImport')->name('issuers.processCsvImport');
    Route::resource('issuers', 'IssuerController');

    // Issuer Detail
    Route::get('issuers/location/{issuer}', 'IssuerController@issuerLocation')->name('issuers.location');
    Route::post('issuers/location/add/form', 'IssuerController@locationAddForm')->name('issuers.locationAddForm');
    Route::post('issuers/location/edit/form', 'IssuerController@locationEditForm')->name('issuers.locationEditForm');
    Route::get('issuers/social/{issuer}', 'IssuerController@issuerSocial')->name('issuers.social');
    Route::get('issuers/media-library/{issuer}', 'IssuerController@issuerMediaLibrary')->name('issuers.mediaLibrary');
    Route::post('issuers/media-library/{issuer}/upload', 'IssuerController@mediaLibraryUpload')->name('issuers.mediaLibraryUpload');

    // Guide Category
    Route::delete('guide-categories/destroy', 'GuideCategoryController@massDestroy')->name('guide-categories.massDestroy');
    Route::post('guide-categories/parse-csv-import', 'GuideCategoryController@parseCsvImport')->name('guide-categories.parseCsvImport');
    Route::post('guide-categories/process-csv-import', 'GuideCategoryController@processCsvImport')->name('guide-categories.processCsvImport');
    Route::resource('guide-categories', 'GuideCategoryController');

    // Guide
    Route::delete('guides/destroy', 'GuideController@massDestroy')->name('guides.massDestroy');
    Route::post('guides/media', 'GuideController@storeMedia')->name('guides.storeMedia');
    Route::post('guides/ckmedia', 'GuideController@storeCKEditorImages')->name('guides.storeCKEditorImages');
    Route::post('guides/parse-csv-import', 'GuideController@parseCsvImport')->name('guides.parseCsvImport');
    Route::post('guides/process-csv-import', 'GuideController@processCsvImport')->name('guides.processCsvImport');
    Route::resource('guides', 'GuideController');

    // Guide Question
    Route::delete('guide-questions/destroy', 'GuideQuestionController@massDestroy')->name('guide-questions.massDestroy');
    Route::post('guide-questions/media', 'GuideQuestionController@storeMedia')->name('guide-questions.storeMedia');
    Route::post('guide-questions/ckmedia', 'GuideQuestionController@storeCKEditorImages')->name('guide-questions.storeCKEditorImages');
    Route::post('guide-questions/parse-csv-import', 'GuideQuestionController@parseCsvImport')->name('guide-questions.parseCsvImport');
    Route::post('guide-questions/process-csv-import', 'GuideQuestionController@processCsvImport')->name('guide-questions.processCsvImport');
    Route::resource('guide-questions', 'GuideQuestionController');

    // Guide Question Answer
    Route::delete('guide-question-answers/destroy', 'GuideQuestionAnswerController@massDestroy')->name('guide-question-answers.massDestroy');
    Route::post('guide-question-answers/parse-csv-import', 'GuideQuestionAnswerController@parseCsvImport')->name('guide-question-answers.parseCsvImport');
    Route::post('guide-question-answers/process-csv-import', 'GuideQuestionAnswerController@processCsvImport')->name('guide-question-answers.processCsvImport');
    Route::resource('guide-question-answers', 'GuideQuestionAnswerController');

    // Guide Customer Progress
    Route::delete('guide-customer-progresss/destroy', 'GuideCustomerProgressController@massDestroy')->name('guide-customer-progresss.massDestroy');
    Route::post('guide-customer-progresss/parse-csv-import', 'GuideCustomerProgressController@parseCsvImport')->name('guide-customer-progresss.parseCsvImport');
    Route::post('guide-customer-progresss/process-csv-import', 'GuideCustomerProgressController@processCsvImport')->name('guide-customer-progresss.processCsvImport');
    Route::resource('guide-customer-progresss', 'GuideCustomerProgressController');

    // Payment Method
    Route::delete('payment-methods/destroy', 'PaymentMethodController@massDestroy')->name('payment-methods.massDestroy');
    Route::post('payment-methods/parse-csv-import', 'PaymentMethodController@parseCsvImport')->name('payment-methods.parseCsvImport');
    Route::post('payment-methods/process-csv-import', 'PaymentMethodController@processCsvImport')->name('payment-methods.processCsvImport');
    Route::resource('payment-methods', 'PaymentMethodController');

    // Vat Percentage
    Route::delete('vat-percentages/destroy', 'VatPercentageController@massDestroy')->name('vat-percentages.massDestroy');
    Route::post('vat-percentages/parse-csv-import', 'VatPercentageController@parseCsvImport')->name('vat-percentages.parseCsvImport');
    Route::post('vat-percentages/process-csv-import', 'VatPercentageController@processCsvImport')->name('vat-percentages.processCsvImport');
    Route::resource('vat-percentages', 'VatPercentageController');

    // Order
    Route::delete('orders/destroy', 'OrderController@massDestroy')->name('orders.massDestroy');
    Route::post('orders/parse-csv-import', 'OrderController@parseCsvImport')->name('orders.parseCsvImport');
    Route::post('orders/process-csv-import', 'OrderController@processCsvImport')->name('orders.processCsvImport');
    Route::resource('orders', 'OrderController');

    // Order Item
    Route::delete('order-items/destroy', 'OrderItemController@massDestroy')->name('order-items.massDestroy');
    Route::post('order-items/parse-csv-import', 'OrderItemController@parseCsvImport')->name('order-items.parseCsvImport');
    Route::post('order-items/process-csv-import', 'OrderItemController@processCsvImport')->name('order-items.processCsvImport');
    Route::resource('order-items', 'OrderItemController');

    // Pass Type
    Route::delete('pass-types/destroy', 'PassTypeController@massDestroy')->name('pass-types.massDestroy');
    Route::post('pass-types/parse-csv-import', 'PassTypeController@parseCsvImport')->name('pass-types.parseCsvImport');
    Route::post('pass-types/process-csv-import', 'PassTypeController@processCsvImport')->name('pass-types.processCsvImport');
    Route::get('pass-types/form', 'PassTypeController@getForm')->name('pass-types.getForm');
    Route::resource('pass-types', 'PassTypeController');

    // Pass Status
    Route::delete('pass-statuses/destroy', 'PassStatusController@massDestroy')->name('pass-statuses.massDestroy');
    Route::post('pass-statuses/parse-csv-import', 'PassStatusController@parseCsvImport')->name('pass-statuses.parseCsvImport');
    Route::post('pass-statuses/process-csv-import', 'PassStatusController@processCsvImport')->name('pass-statuses.processCsvImport');
    // get form
    Route::get('pass-statuses/form', 'PassStatusController@getForm')->name('pass-statuses.getForm');
    Route::resource('pass-statuses', 'PassStatusController');

    // Pass
    Route::delete('passes/destroy', 'PassController@massDestroy')->name('passes.massDestroy');
    Route::post('passes/media', 'PassController@storeMedia')->name('passes.storeMedia');
    Route::post('passes/ckmedia', 'PassController@storeCKEditorImages')->name('passes.storeCKEditorImages');
    Route::post('passes/parse-csv-import', 'PassController@parseCsvImport')->name('passes.parseCsvImport');
    Route::post('passes/process-csv-import', 'PassController@processCsvImport')->name('passes.processCsvImport');

    Route::get('passes/price/{pass}', 'PassController@passPrice')->name('passes.price');
    Route::post('passes/price/edit/form', 'PassController@priceEditForm')->name('passes.priceEditForm');
    Route::get('passes/form', 'PassController@getForm')->name('passes.getForm');
    Route::get('passes/media-library/{pass}', 'PassController@mediaLibrary')->name('passes.mediaLibrary');
    Route::post('passes/media-library/{pass}/upload', 'PassController@mediaLibraryUpload')->name('passes.mediaLibraryUpload');
    Route::get('passes/packages/search', 'PassController@searchPackages')->name('passes.searchPackages');
    Route::get('passes/packages/{pass}', 'PassController@passPackages')->name('passes.packages');
    Route::post('passes/packages/{pass}/add', 'PassController@addPackage')->name('passes.addPackage');
    Route::delete('passes/packages/{pass}/{package}', 'PassController@removePackage')->name('passes.removePackage');
    Route::get('passes/rate-calculator/{pass}', 'PassController@rateCalculator')->name('passes.rateCalculator');
    Route::post('passes/calculate-rate/{pass}', 'PassController@calculateRate')->name('passes.calculateRate');
    Route::post('passes/save-rate/{pass}', 'PassController@saveRate')->name('passes.saveRate');
    Route::post('passes/remove-rate/{pass}', 'PassController@removeRate')->name('passes.removeRate');

    Route::resource('passes', 'PassController');

    // Customer Pass
    Route::delete('customer-passes/destroy', 'CustomerPassController@massDestroy')->name('customer-passes.massDestroy');
    Route::post('customer-passes/parse-csv-import', 'CustomerPassController@parseCsvImport')->name('customer-passes.parseCsvImport');
    Route::post('customer-passes/process-csv-import', 'CustomerPassController@processCsvImport')->name('customer-passes.processCsvImport');
    Route::resource('customer-passes', 'CustomerPassController');

    // Voucher Status
    Route::delete('voucher-statuses/destroy', 'VoucherStatusController@massDestroy')->name('voucher-statuses.massDestroy');
    Route::post('voucher-statuses/parse-csv-import', 'VoucherStatusController@parseCsvImport')->name('voucher-statuses.parseCsvImport');
    Route::post('voucher-statuses/process-csv-import', 'VoucherStatusController@processCsvImport')->name('voucher-statuses.processCsvImport');
    Route::resource('voucher-statuses', 'VoucherStatusController');

    // Voucher Type
    Route::delete('voucher-types/destroy', 'VoucherTypeController@massDestroy')->name('voucher-types.massDestroy');
    Route::post('voucher-types/parse-csv-import', 'VoucherTypeController@parseCsvImport')->name('voucher-types.parseCsvImport');
    Route::post('voucher-types/process-csv-import', 'VoucherTypeController@processCsvImport')->name('voucher-types.processCsvImport');
    Route::resource('voucher-types', 'VoucherTypeController');

    // Voucher
    Route::delete('vouchers/destroy', 'VoucherController@massDestroy')->name('vouchers.massDestroy');
    Route::post('vouchers/parse-csv-import', 'VoucherController@parseCsvImport')->name('vouchers.parseCsvImport');
    Route::post('vouchers/process-csv-import', 'VoucherController@processCsvImport')->name('vouchers.processCsvImport');
    Route::resource('vouchers', 'VoucherController');

    Route::get('team-members', 'TeamMembersController@index')->name('team-members.index');
    Route::post('team-members', 'TeamMembersController@invite')->name('team-members.invite');

    // Setting
    Route::delete('settings/destroy', 'SettingController@massDestroy')->name('settings.massDestroy');
    Route::post('settings/parse-csv-import', 'SettingController@parseCsvImport')->name('settings.parseCsvImport');
    Route::post('settings/process-csv-import', 'SettingController@processCsvImport')->name('settings.processCsvImport');
    Route::resource('settings', 'SettingController');

    // Market
    Route::delete('markets/destroy', 'MarketController@massDestroy')->name('markets.massDestroy');
    Route::post('markets/media', 'MarketController@storeMedia')->name('markets.storeMedia');
    Route::post('markets/ckmedia', 'MarketController@storeCKEditorImages')->name('markets.storeCKEditorImages');
    Route::post('markets/parse-csv-import', 'MarketController@parseCsvImport')->name('markets.parseCsvImport');
    Route::post('markets/process-csv-import', 'MarketController@processCsvImport')->name('markets.processCsvImport');
    Route::get('markets/form', 'MarketController@getForm')->name('markets.getForm');
    Route::resource('markets', 'MarketController');


    // Customer Group
    Route::delete('customer-groups/destroy', 'CustomerGroupController@massDestroy')->name('customer-groups.massDestroy');
    Route::post('customer-groups/parse-csv-import', 'CustomerGroupController@parseCsvImport')->name('customer-groups.parseCsvImport');
    Route::post('customer-groups/process-csv-import', 'CustomerGroupController@processCsvImport')->name('customer-groups.processCsvImport');
    Route::resource('customer-groups', 'CustomerGroupController');


    // Pass Pricing
    Route::delete('pass-pricings/destroy', 'PassPricingController@massDestroy')->name('pass-pricings.massDestroy');
    Route::post('pass-pricings/parse-csv-import', 'PassPricingController@parseCsvImport')->name('pass-pricings.parseCsvImport');
    Route::post('pass-pricings/process-csv-import', 'PassPricingController@processCsvImport')->name('pass-pricings.processCsvImport');
    Route::resource('pass-pricings', 'PassPricingController');

    // Provider Age Group
    Route::delete('provider-age-groups/destroy', 'ProviderAgeGroupController@massDestroy')->name('provider-age-groups.massDestroy');
    Route::post('provider-age-groups/media', 'ProviderAgeGroupController@storeMedia')->name('provider-age-groups.storeMedia');
    Route::post('provider-age-groups/ckmedia', 'ProviderAgeGroupController@storeCKEditorImages')->name('provider-age-groups.storeCKEditorImages');
    Route::post('provider-age-groups/parse-csv-import', 'ProviderAgeGroupController@parseCsvImport')->name('provider-age-groups.parseCsvImport');
    Route::post('provider-age-groups/process-csv-import', 'ProviderAgeGroupController@processCsvImport')->name('provider-age-groups.processCsvImport');
    Route::resource('provider-age-groups', 'ProviderAgeGroupController');

    // Bank
    Route::delete('banks/destroy', 'BankController@massDestroy')->name('banks.massDestroy');
    Route::post('banks/parse-csv-import', 'BankController@parseCsvImport')->name('banks.parseCsvImport');
    Route::post('banks/process-csv-import', 'BankController@processCsvImport')->name('banks.processCsvImport');
    Route::resource('banks', 'BankController');

    // Provider Bank
    Route::delete('provider-banks/destroy', 'ProviderBankController@massDestroy')->name('provider-banks.massDestroy');
    Route::resource('provider-banks', 'ProviderBankController');

});
Route::group(['prefix' => 'profile', 'as' => 'profile.', 'namespace' => 'Auth', 'middleware' => ['auth', '2fa']], function () {
    // Change password
    if (file_exists(app_path('Http/Controllers/Auth/ChangePasswordController.php'))) {
        Route::get('password', 'ChangePasswordController@edit')->name('password.edit');
        Route::post('password', 'ChangePasswordController@update')->name('password.update');
        Route::post('profile', 'ChangePasswordController@updateProfile')->name('password.updateProfile');
        Route::post('profile/destroy', 'ChangePasswordController@destroy')->name('password.destroyProfile');
        Route::post('profile/two-factor', 'ChangePasswordController@toggleTwoFactor')->name('password.toggleTwoFactor');
    }
});
Route::group(['namespace' => 'Auth', 'middleware' => ['auth', '2fa']], function () {
    // Two Factor Authentication
    if (file_exists(app_path('Http/Controllers/Auth/TwoFactorController.php'))) {
        Route::get('two-factor', 'TwoFactorController@show')->name('twoFactor.show');
        Route::post('two-factor', 'TwoFactorController@check')->name('twoFactor.check');
        Route::get('two-factor/resend', 'TwoFactorController@resend')->name('twoFactor.resend');
    }
});
