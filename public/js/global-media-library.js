/**
 * Global Media Library Integration
 * 
 * This script provides easy integration of the Global Media Library
 * into any form that needs media selection functionality.
 */

class GlobalMediaLibrary {
    constructor() {
        this.modalId = 'globalMediaModal';
        this.currentCallback = null;
        this.currentImageType = 'all';
    }

    /**
     * Open the Global Media Library modal
     * @param {Function} callback - Function to call when media is selected
     * @param {string} imageType - Filter by image type (optional)
     */
    open(callback, imageType = 'all') {
        this.currentCallback = callback;
        this.currentImageType = imageType;
        
        // Create modal if it doesn't exist
        if (!document.getElementById(this.modalId)) {
            this.createModal();
        }
        
        // Load media content
        this.loadMediaContent();
        
        // Show modal
        const modal = new bootstrap.Modal(document.getElementById(this.modalId));
        modal.show();
    }

    /**
     * Create the modal HTML structure
     */
    createModal() {
        const modalHtml = `
            <div class="modal fade" id="${this.modalId}" tabindex="-1" aria-labelledby="${this.modalId}Label" aria-hidden="true">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title" id="${this.modalId}Label">Global Media Library</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body" id="${this.modalId}Body">
                            <div class="text-center py-4">
                                <div class="spinner-border" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        document.body.insertAdjacentHTML('beforeend', modalHtml);
        
        // Listen for media selection events
        window.addEventListener('message', (event) => {
            if (event.data.type === 'media-selected' && this.currentCallback) {
                this.currentCallback(event.data.media);
                this.closeModal();
            }
        });
    }

    /**
     * Load media content into modal
     */
    loadMediaContent() {
        const modalBody = document.getElementById(`${this.modalId}Body`);
        
        // Create iframe to load media library
        const iframe = document.createElement('iframe');
        iframe.src = `/admin/media-library/modal?image_type=${this.currentImageType}&select_action=select`;
        iframe.style.width = '100%';
        iframe.style.height = '600px';
        iframe.style.border = 'none';
        
        modalBody.innerHTML = '';
        modalBody.appendChild(iframe);
    }

    /**
     * Close the modal
     */
    closeModal() {
        const modal = bootstrap.Modal.getInstance(document.getElementById(this.modalId));
        if (modal) {
            modal.hide();
        }
    }

    /**
     * Helper method to create a "Browse Global Media" button
     * @param {string} targetInputId - ID of the input field to populate
     * @param {string} imageType - Filter by image type (optional)
     * @param {string} buttonText - Button text (optional)
     */
    static createBrowseButton(targetInputId, imageType = 'all', buttonText = 'Browse Global Media') {
        const button = document.createElement('button');
        button.type = 'button';
        button.className = 'btn btn-outline-primary';
        button.innerHTML = `<i class="fas fa-images"></i> ${buttonText}`;
        
        button.addEventListener('click', () => {
            const library = new GlobalMediaLibrary();
            library.open((media) => {
                const targetInput = document.getElementById(targetInputId);
                if (targetInput) {
                    targetInput.value = media.url;
                    
                    // Trigger change event
                    targetInput.dispatchEvent(new Event('change'));
                    
                    // Show preview if there's a preview element
                    const previewId = targetInputId + '_preview';
                    const preview = document.getElementById(previewId);
                    if (preview) {
                        preview.src = media.url;
                        preview.style.display = 'block';
                    }
                }
            }, imageType);
        });
        
        return button;
    }

    /**
     * Helper method to get the latest banner from global media
     * @param {Function} callback - Function to call with banner data
     */
    static getLatestBanner(callback) {
        fetch('/admin/media-library/latest-banner')
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    callback(data.banner);
                } else {
                    callback(null);
                }
            })
            .catch(error => {
                console.error('Error fetching latest banner:', error);
                callback(null);
            });
    }
}

// Make it globally available
window.GlobalMediaLibrary = GlobalMediaLibrary;

// jQuery integration for backward compatibility
if (typeof $ !== 'undefined') {
    $.fn.globalMediaBrowser = function(options) {
        const settings = $.extend({
            imageType: 'all',
            buttonText: 'Browse Global Media',
            showPreview: true
        }, options);
        
        return this.each(function() {
            const $input = $(this);
            const inputId = $input.attr('id');
            
            if (!inputId) {
                console.error('Input must have an ID for Global Media Library integration');
                return;
            }
            
            // Create browse button
            const button = GlobalMediaLibrary.createBrowseButton(
                inputId, 
                settings.imageType, 
                settings.buttonText
            );
            
            // Insert button after input
            $input.after(button);
            
            // Add preview if requested
            if (settings.showPreview) {
                const preview = $(`<img id="${inputId}_preview" style="display:none; max-width: 200px; margin-top: 10px;" class="img-thumbnail">`);
                $(button).after(preview);
                
                // Show existing image if input has value
                if ($input.val()) {
                    preview.attr('src', $input.val()).show();
                }
            }
        });
    };
}

// Auto-initialize for elements with data-global-media attribute
document.addEventListener('DOMContentLoaded', function() {
    document.querySelectorAll('[data-global-media]').forEach(element => {
        const imageType = element.getAttribute('data-global-media') || 'all';
        const inputId = element.id;
        
        if (inputId) {
            const button = GlobalMediaLibrary.createBrowseButton(inputId, imageType);
            element.parentNode.insertBefore(button, element.nextSibling);
        }
    });
});
