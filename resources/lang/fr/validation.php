<?php

return [
    'accepted'         => "Le champ :attribute doit être accepté.",
    'active_url'       => "Le champ :attribute n'est pas une URL valide.",
    'after'            => "Le champ :attribute doit être une date postérieure au :date.",
    'after_or_equal'   => "Le champ :attribute doit être une date postérieure ou égale au :date.",
    'alpha'            => "Le champ :attribute ne peut contenir que des lettres.",
    'alpha_dash'       => "Le champ :attribute ne peut contenir que des lettres, chiffres, tirets et underscores.",
    'alpha_num'        => "Le champ :attribute ne peut contenir que des lettres et des chiffres.",
    'latin'            => "Le champ :attribute ne peut contenir que des lettres de l'alphabet latin de base.",
    'latin_dash_space' => "Le champ :attribute ne peut contenir que des lettres de l'alphabet latin, chiffres, tirets, espaces et traits d’union.",
    'array'            => "Le champ :attribute doit être un tableau.",
    'before'           => "Le champ :attribute doit être une date antérieure au :date.",
    'before_or_equal'  => "Le champ :attribute doit être une date antérieure ou égale au :date.",
    'between'          => [
        'numeric' => "Le champ :attribute doit être entre :min et :max.",
        'file'    => "Le fichier :attribute doit peser entre :min et :max kilo-octets.",
        'string'  => "Le champ :attribute doit contenir entre :min et :max caractères.",
        'array'   => "Le champ :attribute doit contenir entre :min et :max éléments.",
    ],
    'boolean'          => "Le champ :attribute doit être vrai ou faux.",
    'confirmed'        => "La confirmation du champ :attribute ne correspond pas.",
    'current_password' => "Le mot de passe est incorrect.",
    'date'             => "Le champ :attribute n'est pas une date valide.",
    'date_equals'      => "Le champ :attribute doit être une date égale à :date.",
    'date_format'      => "Le champ :attribute ne correspond pas au format :format.",
    'different'        => "Les champs :attribute et :other doivent être différents.",
    'digits'           => "Le champ :attribute doit contenir :digits chiffres.",
    'digits_between'   => "Le champ :attribute doit contenir entre :min et :max chiffres.",
    'dimensions'       => "Le champ :attribute a des dimensions d'image non valides.",
    'distinct'         => "Le champ :attribute a une valeur en double.",
    'email'            => "Le champ :attribute doit être une adresse e-mail valide.",
    'ends_with'        => "Le champ :attribute doit se terminer par une des valeurs suivantes : :values.",
    'exists'           => "Le champ :attribute sélectionné est invalide.",
    'file'             => "Le champ :attribute doit être un fichier.",
    'filled'           => "Le champ :attribute doit avoir une valeur.",
    'gt' => [
        'numeric' => 'Le champ :attribute doit être supérieur à :value.',
        'file'    => 'Le fichier :attribute doit être supérieur à :value kilo-octets.',
        'string'  => 'Le champ :attribute doit contenir plus de :value caractères.',
        'array'   => 'Le champ :attribute doit contenir plus de :value éléments.',
    ],
    'gte' => [
        'numeric' => 'Le champ :attribute doit être supérieur ou égal à :value.',
        'file'    => 'Le fichier :attribute doit être supérieur ou égal à :value kilo-octets.',
        'string'  => 'Le champ :attribute doit contenir au moins :value caractères.',
        'array'   => 'Le champ :attribute doit contenir :value éléments ou plus.',
    ],
    'image'    => 'Le champ :attribute doit être une image.',
    'in'       => 'Le champ :attribute sélectionné est invalide.',
    'in_array' => 'Le champ :attribute n’existe pas dans :other.',
    'integer'  => 'Le champ :attribute doit être un entier.',
    'ip'       => 'Le champ :attribute doit être une adresse IP valide.',
    'ipv4'     => 'Le champ :attribute doit être une adresse IPv4 valide.',
    'ipv6'     => 'Le champ :attribute doit être une adresse IPv6 valide.',
    'json'     => 'Le champ :attribute doit être une chaîne JSON valide.',
    'lt' => [
        'numeric' => 'Le champ :attribute doit être inférieur à :value.',
        'file'    => 'Le fichier :attribute doit être inférieur à :value kilo-octets.',
        'string'  => 'Le champ :attribute doit contenir moins de :value caractères.',
        'array'   => 'Le champ :attribute doit contenir moins de :value éléments.',
    ],
    'lte' => [
        'numeric' => 'Le champ :attribute doit être inférieur ou égal à :value.',
        'file'    => 'Le fichier :attribute doit être inférieur ou égal à :value kilo-octets.',
        'string'  => 'Le champ :attribute doit contenir au maximum :value caractères.',
        'array'   => 'Le champ :attribute ne doit pas contenir plus de :value éléments.',
    ],
    'max' => [
        'numeric' => 'Le champ :attribute ne peut pas être supérieur à :max.',
        'file'    => 'Le fichier :attribute ne peut pas dépasser :max kilo-octets.',
        'string'  => 'Le champ :attribute ne peut pas contenir plus de :max caractères.',
        'array'   => 'Le champ :attribute ne peut pas contenir plus de :max éléments.',
    ],
    'mimes'     => 'Le champ :attribute doit être un fichier de type : :values.',
    'mimetypes' => 'Le champ :attribute doit être un fichier de type : :values.',
    'min' => [
        'numeric' => 'Le champ :attribute doit être au moins égal à :min.',
        'file'    => 'Le fichier :attribute doit être d’au moins :min kilo-octets.',
        'string'  => 'Le champ :attribute doit contenir au moins :min caractères.',
        'array'   => 'Le champ :attribute doit contenir au moins :min éléments.',
    ],
    'not_in'               => 'Le champ :attribute sélectionné est invalide.',
    'not_regex'            => 'Le format du champ :attribute est invalide.',
    'numeric'              => 'Le champ :attribute doit être un nombre.',
    'password'             => 'Le mot de passe est incorrect.',
    'present'              => 'Le champ :attribute doit être présent.',
    'regex'                => 'Le format du champ :attribute est invalide.',
    'required'             => 'Le champ :attribute est obligatoire.',
    'required_if'          => 'Le champ :attribute est obligatoire quand :other est :value.',
    'required_unless'      => 'Le champ :attribute est obligatoire sauf si :other est dans :values.',
    'required_with'        => 'Le champ :attribute est obligatoire lorsque :values est présent.',
    'required_with_all'    => 'Le champ :attribute est obligatoire lorsque :values est présent.',
    'required_without'     => 'Le champ :attribute est obligatoire lorsque :values n’est pas présent.',
    'required_without_all' => 'Le champ :attribute est obligatoire lorsqu’aucune des valeurs :values n’est présente.',
    'same'                 => 'Les champs :attribute et :other doivent correspondre.',
    'size' => [
        'numeric' => 'Le champ :attribute doit être :size.',
        'file'    => 'Le fichier :attribute doit peser :size kilo-octets.',
        'string'  => 'Le champ :attribute doit contenir :size caractères.',
        'array'   => 'Le champ :attribute doit contenir :size éléments.',
    ],
    'starts_with' => 'Le champ :attribute doit commencer par l’un des éléments suivants : :values.',
    'string'      => 'Le champ :attribute doit être une chaîne de caractères.',
    'timezone'    => 'Le champ :attribute doit être un fuseau horaire valide.',
    'unique'      => 'Le champ :attribute a déjà été pris.',
    'uploaded'    => 'Le téléversement du champ :attribute a échoué.',
    'url'         => 'Le format du champ :attribute est invalide.',
    'uuid'        => 'Le champ :attribute doit être un UUID valide.',
    'custom'      => [
        'attribute-name' => [
            'rule-name' => 'message-personnalisé',
        ],
    ],
    'reserved_word'                  => 'Le champ :attribute contient un mot réservé.',
    'dont_allow_first_letter_number' => 'Le champ ":input" ne peut pas commencer par un chiffre.',
    'exceeds_maximum_number'         => 'Le champ :attribute dépasse la longueur maximale du modèle.',
    'db_column'                      => 'Le champ :attribute ne peut contenir que des lettres latines de base, des chiffres, des tirets, et ne peut pas commencer par un chiffre.',
    'attributes'                     => [],
];
