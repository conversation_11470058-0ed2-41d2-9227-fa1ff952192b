<div class="form-group mb-3 col-6">
    <label class="form-label" for="order_id">{{ trans('cruds.orderItem.fields.order') }}</label>
    <select class="form-select form-select-sm {{ $errors->has('order') ? 'is-invalid' : '' }}" name="order_id" id="order_id" data-control="select2">
        @foreach($orders as $id => $entry)
            <option value="{{ $id }}" {{ old('order_id') == $id ? 'selected' : '' }}>{{ $entry }}</option>
        @endforeach
    </select>
    @if($errors->has('order'))
        <div class="invalid-feedback">
            {{ $errors->first('order') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.orderItem.fields.order_helper') }}</span>
</div>
<div class="form-group mb-3 col-6">
    <label class="form-label" for="package_price_id">{{ trans('cruds.orderItem.fields.package_price') }}</label>
    <select class="form-select form-select-sm {{ $errors->has('package_price') ? 'is-invalid' : '' }}" name="package_price_id" id="package_price_id" data-control="select2">
        @foreach($package_prices as $id => $entry)
            <option value="{{ $id }}" {{ old('package_price_id') == $id ? 'selected' : '' }}>{{ $entry }}</option>
        @endforeach
    </select>
    @if($errors->has('package_price'))
        <div class="invalid-feedback">
            {{ $errors->first('package_price') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.orderItem.fields.package_price_helper') }}</span>
</div>
<div class="form-group mb-3 col-6">
    <label class="form-label" for="package_id">{{ trans('cruds.orderItem.fields.package') }}</label>
    <select class="form-select form-select-sm {{ $errors->has('package') ? 'is-invalid' : '' }}" name="package_id" id="package_id" data-control="select2">
        @foreach($packages as $id => $entry)
            <option value="{{ $id }}" {{ old('package_id') == $id ? 'selected' : '' }}>{{ $entry }}</option>
        @endforeach
    </select>
    @if($errors->has('package'))
        <div class="invalid-feedback">
            {{ $errors->first('package') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.orderItem.fields.package_helper') }}</span>
</div>
<div class="form-group mb-3 col-6">
    <label class="form-label" for="quantity">{{ trans('cruds.orderItem.fields.quantity') }}</label>
    <input class="form-control form-control-sm {{ $errors->has('quantity') ? 'is-invalid' : '' }}" type="number" name="quantity" id="quantity" value="{{ old('quantity', '') }}" step="1">
    @if($errors->has('quantity'))
        <div class="invalid-feedback">
            {{ $errors->first('quantity') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.orderItem.fields.quantity_helper') }}</span>
</div>
<div class="form-group mb-3 col-6">
    <label class="form-label" for="price">{{ trans('cruds.orderItem.fields.price') }}</label>
    <input class="form-control form-control-sm {{ $errors->has('price') ? 'is-invalid' : '' }}" type="number" name="price" id="price" value="{{ old('price', '') }}" step="0.01">
    @if($errors->has('price'))
        <div class="invalid-feedback">
            {{ $errors->first('price') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.orderItem.fields.price_helper') }}</span>
</div>
<div class="form-group mb-3 col-6">
    <label class="form-label" for="vat">{{ trans('cruds.orderItem.fields.vat') }}</label>
    <input class="form-control form-control-sm {{ $errors->has('vat') ? 'is-invalid' : '' }}" type="number" name="vat" id="vat" value="{{ old('vat', '') }}" step="0.01">
    @if($errors->has('vat'))
        <div class="invalid-feedback">
            {{ $errors->first('vat') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.orderItem.fields.vat_helper') }}</span>
</div>
<div class="form-group mb-3 col-6">
    <label class="form-label" for="total_price">{{ trans('cruds.orderItem.fields.total_price') }}</label>
    <input class="form-control form-control-sm {{ $errors->has('total_price') ? 'is-invalid' : '' }}" type="number" name="total_price" id="total_price" value="{{ old('total_price', '') }}" step="0.01">
    @if($errors->has('total_price'))
        <div class="invalid-feedback">
            {{ $errors->first('total_price') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.orderItem.fields.total_price_helper') }}</span>
</div>