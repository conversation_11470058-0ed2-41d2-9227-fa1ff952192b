<div class="form-group mb-3 col-6">
    <label class="form-label" for="customer_id">{{ trans('cruds.order.fields.customer') }}</label>
    <select class="form-select form-select-sm {{ $errors->has('customer') ? 'is-invalid' : '' }}" name="customer_id" id="customer_id" data-control="select2">
        @foreach($customers as $id => $entry)
            <option value="{{ $id }}" {{ old('customer_id') == $id ? 'selected' : '' }}>{{ $entry }}</option>
        @endforeach
    </select>
    @if($errors->has('customer'))
        <div class="invalid-feedback">
            {{ $errors->first('customer') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.order.fields.customer_helper') }}</span>
</div>
<div class="form-group mb-3 col-6">
    <label class="form-label" for="issuer_id">{{ trans('cruds.order.fields.issuer') }}</label>
    <select class="form-select form-select-sm {{ $errors->has('issuer') ? 'is-invalid' : '' }}" name="issuer_id" id="issuer_id" data-control="select2">
        @foreach($issuers as $id => $entry)
            <option value="{{ $id }}" {{ old('issuer_id') == $id ? 'selected' : '' }}>{{ $entry }}</option>
        @endforeach
    </select>
    @if($errors->has('issuer'))
        <div class="invalid-feedback">
            {{ $errors->first('issuer') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.order.fields.issuer_helper') }}</span>
</div>
<div class="form-group mb-3 col-6">
    <label class="form-label" for="package_id">{{ trans('cruds.order.fields.package') }}</label>
    <select class="form-select form-select-sm {{ $errors->has('package') ? 'is-invalid' : '' }}" name="package_id" id="package_id" data-control="select2">
        @foreach($packages as $id => $entry)
            <option value="{{ $id }}" {{ old('package_id') == $id ? 'selected' : '' }}>{{ $entry }}</option>
        @endforeach
    </select>
    @if($errors->has('package'))
        <div class="invalid-feedback">
            {{ $errors->first('package') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.order.fields.package_helper') }}</span>
</div>
<div class="form-group mb-3 col-6">
    <label class="form-label" for="payment_method_id">{{ trans('cruds.order.fields.payment_method') }}</label>
    <select class="form-select form-select-sm {{ $errors->has('payment_method') ? 'is-invalid' : '' }}" name="payment_method_id" id="payment_method_id" data-control="select2">
        @foreach($payment_methods as $id => $entry)
            <option value="{{ $id }}" {{ old('payment_method_id') == $id ? 'selected' : '' }}>{{ $entry }}</option>
        @endforeach
    </select>
    @if($errors->has('payment_method'))
        <div class="invalid-feedback">
            {{ $errors->first('payment_method') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.order.fields.payment_method_helper') }}</span>
</div>
<div class="form-group mb-3 col-6">
    <label class="form-label" for="vat_percentage_id">{{ trans('cruds.order.fields.vat_percentage') }}</label>
    <select class="form-select form-select-sm {{ $errors->has('vat_percentage') ? 'is-invalid' : '' }}" name="vat_percentage_id" id="vat_percentage_id" data-control="select2">
        @foreach($vat_percentages as $id => $entry)
            <option value="{{ $id }}" {{ old('vat_percentage_id') == $id ? 'selected' : '' }}>{{ $entry }}</option>
        @endforeach
    </select>
    @if($errors->has('vat_percentage'))
        <div class="invalid-feedback">
            {{ $errors->first('vat_percentage') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.order.fields.vat_percentage_helper') }}</span>
</div>
<div class="form-group mb-3 col-6">
    <label class="form-label">{{ trans('cruds.order.fields.status') }}</label>
    <select class="form-select form-select-sm {{ $errors->has('status') ? 'is-invalid' : '' }}" name="status" id="status" data-control="select2">
        <option value disabled {{ old('status', null) === null ? 'selected' : '' }}>{{ trans('global.pleaseSelect') }}</option>
        @foreach(App\Models\Order::STATUS_SELECT as $key => $label)
            <option value="{{ $key }}" {{ old('status', 'pending') === (string) $key ? 'selected' : '' }}>{{ $label }}</option>
        @endforeach
    </select>
    @if($errors->has('status'))
        <div class="invalid-feedback">
            {{ $errors->first('status') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.order.fields.status_helper') }}</span>
</div>
<div class="form-group mb-3 col-6">
    <label class="form-label">{{ trans('cruds.order.fields.order_type') }}</label>
    <select class="form-select form-select-sm {{ $errors->has('order_type') ? 'is-invalid' : '' }}" name="order_type" id="order_type" data-control="select2">
        <option value disabled {{ old('order_type', null) === null ? 'selected' : '' }}>{{ trans('global.pleaseSelect') }}</option>
        @foreach(App\Models\Order::ORDER_TYPE_SELECT as $key => $label)
            <option value="{{ $key }}" {{ old('order_type', 'order_customer') === (string) $key ? 'selected' : '' }}>{{ $label }}</option>
        @endforeach
    </select>
    @if($errors->has('order_type'))
        <div class="invalid-feedback">
            {{ $errors->first('order_type') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.order.fields.order_type_helper') }}</span>
</div>
<div class="form-group mb-3 col-6">
    <label class="form-label" for="total_vat">{{ trans('cruds.order.fields.total_vat') }}</label>
    <input class="form-control form-control-sm {{ $errors->has('total_vat') ? 'is-invalid' : '' }}" type="number" name="total_vat" id="total_vat" value="{{ old('total_vat', '') }}" step="0.01">
    @if($errors->has('total_vat'))
        <div class="invalid-feedback">
            {{ $errors->first('total_vat') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.order.fields.total_vat_helper') }}</span>
</div>
<div class="form-group mb-3 col-6">
    <label class="form-label" for="sub_total">{{ trans('cruds.order.fields.sub_total') }}</label>
    <input class="form-control form-control-sm {{ $errors->has('sub_total') ? 'is-invalid' : '' }}" type="number" name="sub_total" id="sub_total" value="{{ old('sub_total', '') }}" step="0.01">
    @if($errors->has('sub_total'))
        <div class="invalid-feedback">
            {{ $errors->first('sub_total') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.order.fields.sub_total_helper') }}</span>
</div>
<div class="form-group mb-3 col-6">
    <label class="form-label" for="total">{{ trans('cruds.order.fields.total') }}</label>
    <input class="form-control form-control-sm {{ $errors->has('total') ? 'is-invalid' : '' }}" type="number" name="total" id="total" value="{{ old('total', '') }}" step="0.01">
    @if($errors->has('total'))
        <div class="invalid-feedback">
            {{ $errors->first('total') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.order.fields.total_helper') }}</span>
</div>