@extends('layouts.admin')

@section('header_title')
    {{ trans('global.edit') }} {{ trans('cruds.package.title') }}
@endsection

@section('styles')
@endsection

@section('menu_wrapper')
    <div id="kt_app_toolbar" class="app-toolbar py-3 py-lg-6">
        <div id="kt_app_toolbar_container">
            <div class="page-title d-flex flex-column justify-content-center flex-wrap me-3">
                <h1 class="page-heading d-flex text-gray-900 fw-bold fs-3 flex-column justify-content-center mt-4 my-0">
                    {{ trans('cruds.package.title') }}
                </h1>
                <ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0 pt-1">
                    <li class="breadcrumb-item text-muted">
                        <a href="/admin/packages" class="text-muted text-hover-primary">{{ trans('cruds.package.title') }}</a> - {{ trans('global.edit') }} Pricing - {{ $package->id }} - {{ $package->title }}
                    </li>
                </ul>
            </div>
        </div>
    </div>
@endsection

@section('content')
    <div class="row g-0">
        <div class="col-lg-12 col-12 pe-lg-2">

            @include('admin.packages.package_tabs', ['package' => $package])
            
            <div class="card custom-card mb-2 p-4 p-sm-5">
                <div class="card-body p-0">
                    <div class="row m-2">
                        <div class="col-4 bg-light border border-gray-300 border-dashed rounded py-3 px-4">
                            <form class="row" method="POST" action="{{ route('admin.package-prices.store') }}" enctype="multipart/form-data">
                                @csrf
                                @include('admin.packagePrices.createForm', ['seasons' => $seasons, 'age_groups' => $age_groups, 'currencies' => $currencies, 'provider_age_groups' => $provider_age_groups, 'customer_groups' => $customer_groups])

                                <input type="hidden" name="package_id" value="{{ $package->id }}">

                                <div class="form-group mb-3">
                                    <button class="btn btn-sm btn-danger" type="submit">
                                        {{ trans('global.save') }}
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <div class="row m-2">
                        <div class="col-12 mt-5 bg-light border border-gray-300 border-dashed rounded py-3 px-4 ">
                            <div id="kt_table_widget_5_table_wrapper" class="dt-container dt-bootstrap5 dt-empty-footer">
                                <div id="" class="table-responsive">
                                    <table class="table align-middle table-row-dashed fs-6 gy-3 dataTable" id="kt_table_widget_5_table" style="width: 100%;">
                                        <thead>
                                            <tr class="text-start text-gray-500 fw-bold fs-7 text-uppercase gs-0">
                                                <th>
                                                    {{ trans('cruds.packagePrice.fields.id') }}
                                                </th>
                                                <th>
                                                    {{ trans('cruds.packagePrice.fields.customer_group') }}
                                                </th>
                                                <th>
                                                    {{ trans('cruds.packagePrice.fields.package') }}
                                                </th>
                                                <th>
                                                    {{ trans('cruds.packagePrice.fields.season') }}
                                                </th>
                                                <th>
                                                    {{ trans('cruds.packagePrice.fields.currency') }}
                                                </th>
                                                <th>
                                                    {{ trans('cruds.packagePrice.fields.provider_age_group') }}
                                                </th>
                                                <th>
                                                    {{ trans('cruds.packagePrice.fields.cost_price') }}
                                                </th>
                                                <th>
                                                    {{ trans('cruds.packagePrice.fields.issuer_price') }}
                                                </th>
                                                <th>
                                                    {{ trans('cruds.packagePrice.fields.price') }}
                                                </th>
                                                <th>
                                                    {{ trans('cruds.packagePrice.fields.is_percentage') }}
                                                </th>
                                                <th>
                                                    {{ trans('cruds.packagePrice.fields.discount_percentage') }}
                                                </th>
                                                <th>
                                                    {{ trans('cruds.packagePrice.fields.start_date') }}
                                                </th>
                                                <th>
                                                    {{ trans('cruds.packagePrice.fields.end_date') }}
                                                </th>
                                                <th>
                                                    ACTIONS
                                                </th>
                                            </tr>

                                        </thead>
                                        <!--end::Table head-->

                                        <!--begin::Table body-->
                                        <tbody class="fw-bold text-gray-600">
                                            @foreach ($package->packagePackagePrices as $key => $packagePrice)
                                                <tr data-entry-id="{{ $packagePrice->id }}">
                                                    <td>
                                                        {{ $packagePrice->id ?? '' }}
                                                    </td>
                                                    <td>
                                                        {{ $packagePrice->customer_group->name ?? '' }}
                                                    </td>
                                                    <td>
                                                        {{ $packagePrice->package->title ?? '' }}
                                                    </td>
                                                    <td>
                                                        {{ $packagePrice->season->name ?? '' }}
                                                    </td>
                                                    <td>
                                                        {{ $packagePrice->currency->name ?? '' }}
                                                    </td>
                                                    <td>
                                                        {{ $packagePrice->provider_age_group->name ?? '' }}
                                                    </td>
                                                    <td>
                                                        {{ $packagePrice->cost_price ?? '' }}
                                                    </td>
                                                    <td>
                                                        {{ $packagePrice->issuer_price ?? '' }}
                                                    </td>
                                                    <td>
                                                        {{ $packagePrice->price ?? '' }}
                                                    </td>
                                                    <td>
                                                        <span style="display:none">{{ $packagePrice->is_percentage ?? '' }}</span>
                                                        <input type="checkbox" disabled="disabled" {{ $packagePrice->is_percentage ? 'checked' : '' }}>
                                                    </td>
                                                    <td>
                                                        {{ $packagePrice->discount_percentage ?? '' }}
                                                    </td>
                                                    <td>
                                                        {{ $packagePrice->start_date ?? '' }}
                                                    </td>
                                                    <td>
                                                        {{ $packagePrice->end_date ?? '' }}
                                                    </td>
                                                    <td>

                                                        @can('package_price_edit')
                                                            <a class="btn btn-icon btn-active-light-primary w-30px h-30px me-3 entity_edit" data-id="{{ $packagePrice->id }}" data-type="address">
                                                                <span>
                                                                    <i class="ki-duotone ki-pencil fs-3">
                                                                        <span class="path1"></span>
                                                                        <span class="path2"></span>
                                                                    </i>
                                                                </span>
                                                            </a>
                                                        @endcan

                                                        @can('package_price_delete')
                                                            <a class="btn btn-icon btn-active-light-primary w-30px h-30px me-3 entity_delete" data-id="{{ $packagePrice->id }}" data-type="address">
                                                                <i class="ki-duotone ki-trash fs-3">
                                                                    <span class="path1"></span>
                                                                    <span class="path2"></span>
                                                                    <span class="path3"></span>
                                                                    <span class="path4"></span>
                                                                    <span class="path5"></span>
                                                                </i>
                                                            </a>
                                                        @endcan
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="modal fade" id="entity_edit_modal" tabindex="-1" aria-labelledby="exampleModalLabel1" aria-hidden="true">
        <div class="modal-dialog modal-lg" id="entity_edit_content">
        </div>
    </div>

    {{-- page-loader --}}
    <div class="page-loader flex-column bg-dark bg-opacity-25 m-0">
        <span class="spinner-border text-primary" role="status"></span>
        <span class="text-gray-800 fs-6 fw-semibold mt-5">Loading...</span>
    </div>
@endsection

@section('scripts')
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.0/jquery.validate.min.js"></script>

    <script>
        $(document).ready(function() {
            new tempusDominus.TempusDominus(document.getElementById("start_date"), {
                display: {
                    viewMode: "calendar",
                    components: {
                        decades: true,
                        year: true,
                        month: true,
                        date: true,
                        hours: false,
                        minutes: false,
                        seconds: false
                    }
                },
                localization: {
                    format: 'yyyy-MM-dd'
                }
            });
            new tempusDominus.TempusDominus(document.getElementById("end_date"), {
                display: {
                    viewMode: "calendar",
                    components: {
                        decades: true,
                        year: true,
                        month: true,
                        date: true,
                        hours: false,
                        minutes: false,
                        seconds: false
                    }
                },
                localization: {
                    format: 'yyyy-MM-dd'
                }
            });
        });
    </script>

    <script>
        $(document).ready(function() {
            $('.entity_edit').click(function(e) {
                e.preventDefault();
                var modal_type = $(this).data('type');
                var entity_id = $(this).data('id');

                KTApp.showPageLoading();

                $.ajax({
                    url: "/admin/packages/price/edit/form",
                    type: "POST",
                    data: {
                        _token: "{{ csrf_token() }}",
                        related_id: "{{ $package->id }}",
                        modal_type: modal_type,
                        entity_id: entity_id,
                    },
                    success: function(data) {
                        $('#entity_edit_content').html(data.modal_content);
                        KTApp.hidePageLoading();
                        $('#entity_edit_modal').modal('show');
                        $('#season_id_edit').select2();
                        $('#currency_id_edit').select2();
                        $('#provider_age_group_id_edit').select2();
                        $('#customer_group_id_edit').select2();
                        $('#issuer_id_edit').select2();
                        new tempusDominus.TempusDominus(document.getElementById("start_date_edit"), {
                            display: {
                                viewMode: "calendar",
                                components: {
                                    decades: true,
                                    year: true,
                                    month: true,
                                    date: true,
                                    hours: false,
                                    minutes: false,
                                    seconds: false
                                }
                            },
                            localization: {
                                format: 'yyyy-MM-dd'
                            }
                        });
                        new tempusDominus.TempusDominus(document.getElementById("end_date_edit"), {
                            display: {
                                viewMode: "calendar",
                                components: {
                                    decades: true,
                                    year: true,
                                    month: true,
                                    date: true,
                                    hours: false,
                                    minutes: false,
                                    seconds: false
                                }
                            },
                            localization: {
                                format: 'yyyy-MM-dd'
                            }
                        });
                    },
                    error: function(xhr, status, error) {
                        console.error("Error: " + error);
                    }
                });

            });
        });
    </script>

    <script>
        $(document).ready(function() {
            $('.entity_delete').click(function(e) {
                e.preventDefault();
                var type = $(this).data('type');

                if (confirm('{{ trans('global.areYouSure') }}')) {
                    var entityId = $(this).data('id');
                    var token = "{{ csrf_token() }}";
                    var actionUrl = "";

                    if (type == 'address') {
                        var actionUrl = "{{ route('admin.package-prices.destroy', '') }}/" + entityId;
                    }

                    var form = $('<form>', {
                        'method': 'POST',
                        'action': actionUrl
                    });

                    form.append($('<input>', {
                        'type': 'hidden',
                        'name': '_method',
                        'value': 'DELETE'
                    }));

                    form.append($('<input>', {
                        'type': 'hidden',
                        'name': '_token',
                        'value': token
                    }));

                    form.appendTo('body').submit();
                }
            });
        });
    </script>
@endsection
