@extends('layouts.admin')

@section('header_title')
{{ trans('global.edit') }} {{ trans('cruds.role.title_singular') }}
@endsection

@section('styles')
@endsection

@section('menu_wrapper')
    <div id="kt_app_toolbar" class="app-toolbar py-3 py-lg-6">
        <div id="kt_app_toolbar_container"  >
            <div class="page-title d-flex flex-column justify-content-center flex-wrap me-3">
                <h1 class="page-heading d-flex text-gray-900 fw-bold fs-3 flex-column justify-content-center my-0">
                    {{ trans('cruds.role.title_singular') }}
                </h1>
                <ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0 pt-1">
                    <li class="breadcrumb-item text-muted">
                        <a href="/admin/roles" class="text-muted text-hover-primary">{{ trans('cruds.role.title_singular') }}</a>
                    </li>
                    <li class="breadcrumb-item">
                        <span class="bullet bg-gray-500 w-5px h-2px"></span>
                    </li>
                    <li class="breadcrumb-item text-muted">{{ trans('global.edit') }}
                        {{ trans('cruds.role.title_singular') }}</li>
                </ul>
            </div>

        </div>
    </div>
@endsection

@section('content')
    <div class="d-flex flex-column flex-column-fluid">
        <div id="kt_app_content" class="app-content flex-column-fluid">
            <div id="kt_app_content_container" class="container-xxxl">
                <div class="row g-0">
                    <div class="col-lg-6 col-6 pe-lg-2">
                        <div class="card mb-2 p-4 p-sm-5">
                            <div class="card-head d-flex align-items-center justify-content-between mb-5 mb-sm-6">
                                <h4> {{ trans('global.create') }} {{ trans('cruds.role.title_singular') }}</h4>
                            </div>
                            <div class="card-body p-0">
                                <form method="POST" action="{{ route('admin.roles.update', [$role->id]) }}" enctype="multipart/form-data">
                                    @method('PUT')
                                    @csrf
                                    <div class="form-group mb-3">
                                        <label class="form-label required" for="title">{{ trans('cruds.role.fields.title') }}</label>
                                        <input class="form-control form-control-sm {{ $errors->has('title') ? 'is-invalid' : '' }}" type="text" name="title" id="title" value="{{ old('title', $role->title) }}" required>
                                        @if($errors->has('title'))
                                            <div class="invalid-feedback">
                                                {{ $errors->first('title') }}
                                            </div>
                                        @endif
                                        <span class="help-block">{{ trans('cruds.role.fields.title_helper') }}</span>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label class="form-label required">{{ trans('cruds.role.fields.permissions') }}</label>
                                        <div style="padding-bottom: 4px">
                                            
                                        </div>
                        
                                        <!-- Grouped Permissions by Prefix -->
                                        @php
                                            $groupedPermissions = [];
                                            foreach($permissions as $id => $permission) {
                                                // Extract the prefix from the permission name
                                                $prefix = explode('_', $permission)[0];
                                                $groupedPermissions[$prefix][] = ['id' => $id, 'name' => $permission];
                                            }
                                        @endphp
                        
                                        <!-- Display Permissions in Table Format -->
                                        <table class="table table-bordered">
                                            <thead>
                                                <tr>
                                                    <th></th>
                                                    <th class="text-end">
                                                        <span class="btn btn-info btn-sm select-all m-0" >{{ trans('global.select_all') }}</span>
                                                        <span class="btn btn-info btn-sm deselect-all m-0">{{ trans('global.deselect_all') }}</span>
                                                    </th>
                                                </tr>
                                                <tr>
                                                    <th>Section</th>
                                                    <th>Permissions</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach($groupedPermissions as $groupName => $groupPermissions)
                                                    <tr>
                                                        <td rowspan="{{ count($groupPermissions) + 1 }}">{{ strtoupper($groupName) }}</td>
                                                    </tr>
                                                    @foreach($groupPermissions as $perm)
                                                        <tr>
                                                            <td>
                                                                <div class="form-check">
                                                                    <input class="form-check-input {{ $errors->has('permissions') ? 'is-invalid' : '' }}" 
                                                                           type="checkbox" 
                                                                           name="permissions[]" 
                                                                           value="{{ $perm['id'] }}" 
                                                                           id="permission-{{ $perm['id'] }}" 
                                                                           {{ (in_array($perm['id'], old('permissions', [])) || $role->permissions->contains($perm['id'])) ? 'checked' : '' }}>
                                                                    <label class="form-check-label text-capitalize" for="permission-{{ $perm['id'] }}">
                                                                        {{ str_replace('_', ' ', $perm['name']) }}
                                                                    </label>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                    @endforeach
                                                @endforeach
                                            </tbody>
                                        </table>
                        
                                        @if($errors->has('permissions'))
                                            <div class="invalid-feedback">
                                                {{ $errors->first('permissions') }}
                                            </div>
                                        @endif
                                        <span class="help-block">{{ trans('cruds.role.fields.permissions_helper') }}</span>
                                    </div>
                        
                                    <div class="form-group mb-3">
                                        <button class="btn btn-danger btn-sm" type="submit">
                                            {{ trans('global.save') }}
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
    <script>
        document.querySelector('.select-all').addEventListener('click', function() {
            document.querySelectorAll('.form-check-input').forEach(function(checkbox) {
                checkbox.checked = true;
            });
        });

        document.querySelector('.deselect-all').addEventListener('click', function() {
            document.querySelectorAll('.form-check-input').forEach(function(checkbox) {
                checkbox.checked = false;
            });
        });
    </script>
@endsection
