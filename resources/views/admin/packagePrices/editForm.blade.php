<div class="form-group mb-3 col-6">
    <label class="form-label" for="season_id">{{ trans('cruds.packagePrice.fields.season') }}</label>
    <select class="form-select form-select-sm {{ $errors->has('season') ? 'is-invalid' : '' }}" name="season_id" id="season_id_edit" data-control="select2">
        @foreach($seasons as $id => $entry)
            <option value="{{ $id }}" {{ (old('season_id') ? old('season_id') : $packagePrice->season->id ?? '') == $id ? 'selected' : '' }}>{{ $entry }}</option>
        @endforeach
    </select>
    @if($errors->has('season'))
        <div class="invalid-feedback">
            {{ $errors->first('season') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.packagePrice.fields.season_helper') }}</span>
</div>
<div class="form-group mb-3 col-6">
    <label class="form-label" for="currency_id">{{ trans('cruds.packagePrice.fields.currency') }}</label>
    <select class="form-select form-select-sm {{ $errors->has('currency') ? 'is-invalid' : '' }}" name="currency_id" id="currency_id_edit" data-control="select2">
        @foreach($currencies as $id => $entry)
            <option value="{{ $id }}" {{ (old('currency_id') ? old('currency_id') : $packagePrice->currency->id ?? '') == $id ? 'selected' : '' }}>{{ $entry }}</option>
        @endforeach
    </select>
    @if($errors->has('currency'))
        <div class="invalid-feedback">
            {{ $errors->first('currency') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.packagePrice.fields.currency_helper') }}</span>
</div>
<div class="form-group mb-3 col-6">
    <label class="form-label" for="provider_age_group_id">{{ trans('cruds.packagePrice.fields.provider_age_group') }}</label>
    <select class="form-select form-select-sm {{ $errors->has('provider_age_group') ? 'is-invalid' : '' }}" name="provider_age_group_id" id="provider_age_group_id_edit" data-control="select2">
        @foreach($provider_age_groups as $id => $entry)
            <option value="{{ $id }}" {{ (old('provider_age_group_id') ? old('provider_age_group_id') : $packagePrice->provider_age_group->id ?? '') == $id ? 'selected' : '' }}>{{ $entry }}</option>
        @endforeach
    </select>
    @if($errors->has('provider_age_group'))
        <div class="invalid-feedback">
            {{ $errors->first('provider_age_group') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.packagePrice.fields.provider_age_group_helper') }}</span>
</div>
<div class="form-group mb-3 col-6">
    <label class="form-label" for="customer_group_id">{{ trans('cruds.packagePrice.fields.customer_group') }}</label>
    <select class="form-select form-select-sm {{ $errors->has('customer_group') ? 'is-invalid' : '' }}" name="customer_group_id" id="customer_group_id_edit" data-control="select2">
        @foreach($customer_groups as $id => $entry)
            <option value="{{ $id }}" {{ (old('customer_group_id') ? old('customer_group_id') : $packagePrice->customer_group->id ?? '') == $id ? 'selected' : '' }}>{{ $entry }}</option>
        @endforeach
    </select>
    @if($errors->has('customer_group'))
        <div class="invalid-feedback">
            {{ $errors->first('customer_group') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.packagePrice.fields.customer_group_helper') }}</span>
</div>
<div class="form-group mb-3 col-6">
    <label class="form-label" for="issuer_id">{{ trans('cruds.packagePrice.fields.issuer') }}</label>
    <select class="form-select form-select-sm {{ $errors->has('issuer') ? 'is-invalid' : '' }}" name="issuer_id" id="issuer_id_edit" data-control="select2">
        @foreach($issuers as $id => $entry)
            <option value="{{ $id }}" {{ (old('issuer_id') ? old('issuer_id') : $packagePrice->issuer->id ?? '') == $id ? 'selected' : '' }}>{{ $entry }}</option>
        @endforeach
    </select>
    @if($errors->has('issuer'))
        <div class="invalid-feedback">
            {{ $errors->first('issuer') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.packagePrice.fields.issuer_helper') }}</span>
</div>
<div class="form-group mb-3 col-6">
    <label class="form-label" for="price">{{ trans('cruds.packagePrice.fields.price') }}</label>
    <input class="form-control form-control-sm {{ $errors->has('price') ? 'is-invalid' : '' }}" type="number" name="price" id="price" value="{{ old('price', $packagePrice->price) }}" step="0.01">
    @if($errors->has('price'))
        <div class="invalid-feedback">
            {{ $errors->first('price') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.packagePrice.fields.price_helper') }}</span>
</div>
<div class="form-group mb-3 col-6">
    <label class="form-label" for="cost_price">{{ trans('cruds.packagePrice.fields.cost_price') }}</label>
    <input class="form-control form-control-sm {{ $errors->has('cost_price') ? 'is-invalid' : '' }}" type="number" name="cost_price" id="cost_price_edit" value="{{ old('cost_price', $packagePrice->cost_price) }}" step="0.01">
    @if($errors->has('cost_price'))
        <div class="invalid-feedback">
            {{ $errors->first('cost_price') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.packagePrice.fields.cost_price_helper') }}</span>
</div>
<div class="form-group mb-3 col-6">
    <label class="form-label" for="issuer_price">{{ trans('cruds.packagePrice.fields.issuer_price') }}</label>
    <input class="form-control form-control-sm {{ $errors->has('issuer_price') ? 'is-invalid' : '' }}" type="number" name="issuer_price" id="issuer_price_edit" value="{{ old('issuer_price', $packagePrice->issuer_price) }}" step="0.01">
    @if($errors->has('issuer_price'))
        <div class="invalid-feedback">
            {{ $errors->first('issuer_price') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.packagePrice.fields.issuer_price_helper') }}</span>
</div>
<div class="form-group mb-3 col-12"></div>

<div class="form-group mb-3 col-6">
    <label class="form-label" for="start_date">{{ trans('cruds.packagePrice.fields.start_date') }}</label>
    <input class="form-control form-control-sm {{ $errors->has('start_date') ? 'is-invalid' : '' }}" type="text" name="start_date" id="start_date_edit" value="{{ old('start_date', $packagePrice->start_date) }}">
    @if($errors->has('start_date'))
        <div class="invalid-feedback">
            {{ $errors->first('start_date') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.packagePrice.fields.start_date_helper') }}</span>
</div>
<div class="form-group mb-3 col-6">
    <label class="form-label" for="end_date">{{ trans('cruds.packagePrice.fields.end_date') }}</label>
    <input class="form-control form-control-sm {{ $errors->has('end_date') ? 'is-invalid' : '' }}" type="text" name="end_date" id="end_date_edit" value="{{ old('end_date', $packagePrice->end_date) }}">
    @if($errors->has('end_date'))
        <div class="invalid-feedback">
            {{ $errors->first('end_date') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.packagePrice.fields.end_date_helper') }}</span>
</div>
<div class="form-group mb-3 col-6">
    <label class="form-label" for="discount_percentage">{{ trans('cruds.packagePrice.fields.discount_percentage') }}</label>
    <input class="form-control form-control-sm {{ $errors->has('discount_percentage') ? 'is-invalid' : '' }}" type="text" name="discount_percentage" id="discount_percentage_edit" value="{{ old('discount_percentage', $packagePrice->discount_percentage) }}">
    @if($errors->has('discount_percentage'))
        <div class="invalid-feedback">
            {{ $errors->first('discount_percentage') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.packagePrice.fields.discount_percentage_helper') }}</span>
</div>
<div class="form-group mt-5 col-6">
    <div class="form-check {{ $errors->has('is_percentage') ? 'is-invalid' : '' }}">
        <input type="hidden" name="is_percentage" value="0">
        <input class="form-check-input" type="checkbox" name="is_percentage" id="is_percentage_edit" value="1" {{ $packagePrice->is_percentage || old('is_percentage', 0) === 1 ? 'checked' : '' }}>
        <label class="form-label" class="form-check-label" for="is_percentage">{{ trans('cruds.packagePrice.fields.is_percentage') }}</label>
    </div>
    @if($errors->has('is_percentage'))
        <div class="invalid-feedback">
            {{ $errors->first('is_percentage') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.packagePrice.fields.is_percentage_helper') }}</span>
</div>
