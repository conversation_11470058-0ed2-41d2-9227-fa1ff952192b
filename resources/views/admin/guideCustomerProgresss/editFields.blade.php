<div class="form-group mb-3">
    <label class="form-label" for="customer_id">{{ trans('cruds.guideCustomerProgress.fields.customer') }}</label>
    <select class="form-select form-select-sm {{ $errors->has('customer') ? 'is-invalid' : '' }}" name="customer_id" id="customer_id" data-control="select2">
        @foreach($customers as $id => $entry)
            <option value="{{ $id }}" {{ (old('customer_id') ? old('customer_id') : $guideCustomerProgress->customer?->id) == $id ? 'selected' : '' }}>{{ $entry }}</option>
        @endforeach
    </select>
    @if($errors->has('customer'))
        <div class="invalid-feedback">
            {{ $errors->first('customer') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.guideCustomerProgress.fields.customer_helper') }}</span>
</div>
<div class="form-group mb-3">
    <label class="form-label" for="answer">{{ trans('cruds.guideCustomerProgress.fields.answer') }}</label>
    <input class="form-control form-control-sm {{ $errors->has('answer') ? 'is-invalid' : '' }}" type="text" name="answer" id="answer" value="{{ old('answer', $guideCustomerProgress->answer) }}">
    @if($errors->has('answer'))
        <div class="invalid-feedback">
            {{ $errors->first('answer') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.guideCustomerProgress.fields.answer_helper') }}</span>
</div>
<div class="form-group mb-3">
    <label class="form-label" for="answered_at">{{ trans('cruds.guideCustomerProgress.fields.answered_at') }}</label>
    <input class="form-control form-control-sm datetime {{ $errors->has('answered_at') ? 'is-invalid' : '' }}" type="text" name="answered_at" id="answered_at" value="{{ old('answered_at', $guideCustomerProgress->answered_at) }}">
    @if($errors->has('answered_at'))
        <div class="invalid-feedback">
            {{ $errors->first('answered_at') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.guideCustomerProgress.fields.answered_at_helper') }}</span>
</div>
<div class="form-group mb-3">
    <label class="form-label" for="guide_id">{{ trans('cruds.guideCustomerProgress.fields.guide') }}</label>
    <select class="form-select form-select-sm {{ $errors->has('guide') ? 'is-invalid' : '' }}" name="guide_id" id="guide_id" data-control="select2">
        @foreach($guides as $id => $entry)
            <option value="{{ $id }}" {{ (old('guide_id') ? old('guide_id') : $guideCustomerProgress->guide->id ?? '') == $id ? 'selected' : '' }}>{{ $entry }}</option>
        @endforeach
    </select>
    @if($errors->has('guide'))
        <div class="invalid-feedback">
            {{ $errors->first('guide') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.guideCustomerProgress.fields.guide_helper') }}</span>
</div>