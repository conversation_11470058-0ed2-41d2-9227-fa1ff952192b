<div class="form-group mb-3">
    <label class="form-label" for="guide_id">{{ trans('cruds.guideQuestion.fields.guide') }}</label>
    <select class="form-select form-select-sm {{ $errors->has('guide') ? 'is-invalid' : '' }}" name="guide_id" id="guide_id" data-control="select2">
        @foreach($guides as $id => $entry)
            <option value="{{ $id }}" {{ old('guide_id') == $id ? 'selected' : '' }}>{{ $entry }}</option>
        @endforeach
    </select>
    @if($errors->has('guide'))
        <div class="invalid-feedback">
            {{ $errors->first('guide') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.guideQuestion.fields.guide_helper') }}</span>
</div>
<div class="form-group mb-3">
    <label class="form-label" for="title">{{ trans('cruds.guideQuestion.fields.title') }}</label>
    <input class="form-control form-control-sm {{ $errors->has('title') ? 'is-invalid' : '' }}" type="text" name="title" id="title" value="{{ old('title', '') }}">
    @if($errors->has('title'))
        <div class="invalid-feedback">
            {{ $errors->first('title') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.guideQuestion.fields.title_helper') }}</span>
</div>
<div class="form-group mb-3">
    <label class="form-label" for="image_1">{{ trans('cruds.guideQuestion.fields.image_1') }}</label>
    <div class="needsclick dropzone {{ $errors->has('image_1') ? 'is-invalid' : '' }}" id="image_1-dropzone">
    </div>
    @if($errors->has('image_1'))
        <div class="invalid-feedback">
            {{ $errors->first('image_1') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.guideQuestion.fields.image_1_helper') }}</span>
</div>
<div class="form-group mb-3">
    <label class="form-label" for="image_2">{{ trans('cruds.guideQuestion.fields.image_2') }}</label>
    <div class="needsclick dropzone {{ $errors->has('image_2') ? 'is-invalid' : '' }}" id="image_2-dropzone">
    </div>
    @if($errors->has('image_2'))
        <div class="invalid-feedback">
            {{ $errors->first('image_2') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.guideQuestion.fields.image_2_helper') }}</span>
</div>
<div class="form-group mb-3">
    <label class="form-label" for="position">{{ trans('cruds.guideQuestion.fields.position') }}</label>
    <input class="form-control form-control-sm {{ $errors->has('position') ? 'is-invalid' : '' }}" type="number" name="position" id="position" value="{{ old('position', '') }}" step="1">
    @if($errors->has('position'))
        <div class="invalid-feedback">
            {{ $errors->first('position') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.guideQuestion.fields.position_helper') }}</span>
</div>