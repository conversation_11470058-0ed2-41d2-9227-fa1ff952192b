<div class="form-group mb-3 col-6">
    <label class="form-label">{{ trans('cruds.customerAddress.fields.type') }}</label>
    <select class="form-select form-select-sm {{ $errors->has('type') ? 'is-invalid' : '' }}" name="type" id="type" data-control="select2">
        <option value disabled {{ old('type', null) === null ? 'selected' : '' }}>{{ trans('global.pleaseSelect') }}</option>
        @foreach(App\Models\CustomerAddress::TYPE_SELECT as $key => $label)
            <option value="{{ $key }}" {{ old('type', $customerAddress->type) === (string) $key ? 'selected' : '' }}>{{ $label }}</option>
        @endforeach
    </select>
    @if($errors->has('type'))
        <div class="invalid-feedback">
            {{ $errors->first('type') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.customerAddress.fields.type_helper') }}</span>
</div>
<div class="form-group mb-3 col-6">
    <label class="form-label" for="address_1">{{ trans('cruds.customerAddress.fields.address_1') }}</label>
    <input class="form-control form-control-sm {{ $errors->has('address_1') ? 'is-invalid' : '' }}" type="text" name="address_1" id="address_1" value="{{ old('address_1', $customerAddress->address_1) }}">
    @if($errors->has('address_1'))
        <div class="invalid-feedback">
            {{ $errors->first('address_1') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.customerAddress.fields.address_1_helper') }}</span>
</div>
<div class="form-group mb-3 col-6">
    <label class="form-label" for="address_2">{{ trans('cruds.customerAddress.fields.address_2') }}</label>
    <input class="form-control form-control-sm {{ $errors->has('address_2') ? 'is-invalid' : '' }}" type="text" name="address_2" id="address_2" value="{{ old('address_2', $customerAddress->address_2) }}">
    @if($errors->has('address_2'))
        <div class="invalid-feedback">
            {{ $errors->first('address_2') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.customerAddress.fields.address_2_helper') }}</span>
</div>
<div class="form-group mb-3 col-6">
    <label class="form-label" for="state">{{ trans('cruds.customerAddress.fields.state') }}</label>
    <input class="form-control form-control-sm {{ $errors->has('state') ? 'is-invalid' : '' }}" type="text" name="state" id="state" value="{{ old('state', $customerAddress->state) }}">
    @if($errors->has('state'))
        <div class="invalid-feedback">
            {{ $errors->first('state') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.customerAddress.fields.state_helper') }}</span>
</div>
<div class="form-group mb-3 col-6">
    <label class="form-label" for="city">{{ trans('cruds.customerAddress.fields.city') }}</label>
    <input class="form-control form-control-sm {{ $errors->has('city') ? 'is-invalid' : '' }}" type="text" name="city" id="city" value="{{ old('city', $customerAddress->city) }}">
    @if($errors->has('city'))
        <div class="invalid-feedback">
            {{ $errors->first('city') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.customerAddress.fields.city_helper') }}</span>
</div>
<div class="form-group mb-3 col-6">
    <label class="form-label" for="postal_code">{{ trans('cruds.customerAddress.fields.postal_code') }}</label>
    <input class="form-control form-control-sm {{ $errors->has('postal_code') ? 'is-invalid' : '' }}" type="text" name="postal_code" id="postal_code" value="{{ old('postal_code', $customerAddress->postal_code) }}">
    @if($errors->has('postal_code'))
        <div class="invalid-feedback">
            {{ $errors->first('postal_code') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.customerAddress.fields.postal_code_helper') }}</span>
</div>
<div class="form-group mb-3 col-6">
    <label class="form-label" for="latitude">{{ trans('cruds.customerAddress.fields.latitude') }}</label>
    <input class="form-control form-control-sm {{ $errors->has('latitude') ? 'is-invalid' : '' }}" type="text" name="latitude" id="latitude" value="{{ old('latitude', $customerAddress->latitude) }}">
    @if($errors->has('latitude'))
        <div class="invalid-feedback">
            {{ $errors->first('latitude') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.customerAddress.fields.latitude_helper') }}</span>
</div>
<div class="form-group mb-3 col-6">
    <label class="form-label" for="longitude">{{ trans('cruds.customerAddress.fields.longitude') }}</label>
    <input class="form-control form-control-sm {{ $errors->has('longitude') ? 'is-invalid' : '' }}" type="text" name="longitude" id="longitude" value="{{ old('longitude', $customerAddress->longitude) }}">
    @if($errors->has('longitude'))
        <div class="invalid-feedback">
            {{ $errors->first('longitude') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.customerAddress.fields.longitude_helper') }}</span>
</div>
<div class="form-group mb-3 col-6">
    <label class="form-label" for="country_id">{{ trans('cruds.customerAddress.fields.country') }}</label>
    <select class="form-select form-select-sm {{ $errors->has('country') ? 'is-invalid' : '' }}" name="country_id" id="country_id" data-control="select2">
        @foreach($countries as $id => $entry)
            <option value="{{ $id }}" {{ (old('country_id') ? old('country_id') : $customerAddress->country->id ?? '') == $id ? 'selected' : '' }}>{{ $entry }}</option>
        @endforeach
    </select>
    @if($errors->has('country'))
        <div class="invalid-feedback">
            {{ $errors->first('country') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.customerAddress.fields.country_helper') }}</span>
</div>