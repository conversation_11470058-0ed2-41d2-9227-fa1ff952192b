@extends('layouts.admin')

@section('header_title')
    {{ trans('global.edit') }} {{ trans('cruds.customerAddress.title') }}
@endsection

@section('styles')
@endsection

@section('menu_wrapper')
    <div id="kt_app_toolbar" class="app-toolbar py-3 py-lg-6">
        <div id="kt_app_toolbar_container"  >
            <div class="page-title d-flex flex-column justify-content-center flex-wrap me-3">
                <h1 class="page-heading d-flex text-gray-900 fw-bold fs-3 flex-column justify-content-center my-0">
                    {{ trans('cruds.customerAddress.title') }}
                </h1>
                <ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0 pt-1">
                    <li class="breadcrumb-item text-muted">
                        <a href="/admin/customer-addresses" class="text-muted text-hover-primary">{{ trans('cruds.customerAddress.title') }}</a> - {{ trans('global.edit') }} - {{ $customerAddress->id }} - {{ $customerAddress->customer->first_name ?? '' }} {{ $customerAddress->customer->last_name ?? '' }}
                    </li>
                </ul>
            </div>
        </div>
    </div>
@endsection

@section('content')
    <div class="d-flex flex-column flex-column-fluid">
        <div id="kt_app_content" class="app-content flex-column-fluid">
            <div id="kt_app_content_container" class="container-xxxl">
                <div class="row g-0">
                    <div class="col-lg-6 col-6 pe-lg-2">
                        <div class="card mb-2 p-4 p-sm-5">
                            <div class="card-head d-flex align-items-center justify-content-between mb-5 mb-sm-6">
                                <h4> {{ trans('global.edit') }} {{ trans('cruds.customerAddress.title') }}</h4>
                            </div>
                            <div class="card-body p-0">
								<form method="POST" class="row" action="{{ route("admin.customer-addresses.update", [$customerAddress->id]) }}" enctype="multipart/form-data">
									@method('PUT')
									@csrf
									<div class="form-group mb-3 col-12">
										<label class="form-label" for="customer_id">{{ trans('cruds.customerAddress.fields.customer') }}</label>
										<select class="form-select form-select-sm {{ $errors->has('customer') ? 'is-invalid' : '' }}" name="customer_id" id="customer_id" data-control="select2">
											@foreach($customers as $id => $entry)
												<option value="{{ $id }}" {{ (old('customer_id') ? old('customer_id') : $customerAddress->customer->id ?? '') == $id ? 'selected' : '' }}>{{ $entry }}</option>
											@endforeach
										</select>
										@if($errors->has('customer'))
											<div class="invalid-feedback">
												{{ $errors->first('customer') }}
											</div>
										@endif
										<span class="help-block">{{ trans('cruds.customerAddress.fields.customer_helper') }}</span>
									</div>

									@include('admin.customerAddresses.editForm', ['customerAddress' => $customerAddress])

									<div class="form-group mb-3">
										<button class="btn btn-sm btn-danger" type="submit">
											{{ trans('global.save') }}
										</button>
									</div>
								</form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('scripts')
    <script>
        document.querySelector('.select-all').addEventListener('click', function() {
            document.querySelectorAll('.form-check-input').forEach(function(checkbox) {
                checkbox.checked = true;
            });
        });

        document.querySelector('.deselect-all').addEventListener('click', function() {
            document.querySelectorAll('.form-check-input').forEach(function(checkbox) {
                checkbox.checked = false;
            });
        });
    </script>
@endsection
