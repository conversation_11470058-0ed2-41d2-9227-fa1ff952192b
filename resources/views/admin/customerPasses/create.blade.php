@extends('layouts.admin')

@section('header_title')
{{ trans('global.create') }} {{ trans('cruds.customerPass.title_singular') }}
@endsection

@section('styles')
@endsection

@section('menu_wrapper')
    <div id="kt_app_toolbar" class="app-toolbar py-3 py-lg-6">
        <div id="kt_app_toolbar_container"  >
            <div class="page-title d-flex flex-column justify-content-center flex-wrap me-3">
                <h1 class="page-heading d-flex text-gray-900 fw-bold fs-3 flex-column justify-content-center my-0">
                    {{ trans('cruds.customerPass.title_singular') }}
                </h1>
                <ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0 pt-1">
                    <li class="breadcrumb-item text-muted">
                        <a href="/admin/customer-passes" class="text-muted text-hover-primary">{{ trans('cruds.customerPass.title_singular') }}</a>
                    </li>
                    <li class="breadcrumb-item">
                        <span class="bullet bg-gray-500 w-5px h-2px"></span>
                    </li>
                    <li class="breadcrumb-item text-muted">{{ trans('global.create') }}
                        {{ trans('cruds.customerPass.title_singular') }}
                    </li>
                </ul>
            </div>
        </div>
    </div>
@endsection

@section('content')
    <div class="d-flex flex-column flex-column-fluid">
        <div id="kt_app_content" class="app-content flex-column-fluid">
            <div id="kt_app_content_container" class="container-xxxl">
                <div class="row g-0">
                    <div class="col-lg-6 col-6 pe-lg-2">
                        <div class="card mb-2 p-4 p-sm-5">
                            <div class="card-head d-flex align-items-center justify-content-between mb-5 mb-sm-6">
                                <h4> {{ trans('global.create') }} {{ trans('cruds.customerPass.title_singular') }}</h4>
                            </div>
                            <div class="card-body p-0">
                                <form method="POST" class="row" action="{{ route("admin.customer-passes.store") }}" enctype="multipart/form-data">
									@csrf
									@include('admin.customerPasses.createForm')
									<div class="form-group mb-3">
										<button class="btn btn-sm btn-danger" type="submit">
											{{ trans('global.save') }}
										</button>
									</div>
								</form>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('scripts')

    <script>
        $(document).ready(function() {
            new tempusDominus.TempusDominus(document.getElementById("start_date"), {
                display: {
                    viewMode: "calendar",
                    components: {
                        decades: true,
                        year: true,
                        month: true,
                        date: true,
                        hours: false,
                        minutes: false,
                        seconds: false
                    }
                },
                localization: {
                    format: 'yyyy-MM-dd'
                }
            });
        });
    </script>

    <script>
        $(document).ready(function() {
            new tempusDominus.TempusDominus(document.getElementById("end_date"), {
                display: {
                    viewMode: "calendar",
                    components: {
                        decades: true,
                        year: true,
                        month: true,
                        date: true,
                        hours: false,
                        minutes: false,
                        seconds: false
                    }
                },
                localization: {
                    format: 'yyyy-MM-dd'
                }
            });
        });
    </script>
    
@endsection
