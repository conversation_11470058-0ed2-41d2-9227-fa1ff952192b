<div class="form-group mb-3 col-6">
    <label class="form-label" for="customer_id">{{ trans('cruds.customerPass.fields.customer') }}</label>
    <select class="form-select form-select-sm {{ $errors->has('customer') ? 'is-invalid' : '' }}" name="customer_id" id="customer_id" data-control="select2">
        @foreach($customers as $id => $entry)
            <option value="{{ $id }}" {{ old('customer_id') == $id ? 'selected' : '' }}>{{ $entry }}</option>
        @endforeach
    </select>
    @if($errors->has('customer'))
        <div class="invalid-feedback">
            {{ $errors->first('customer') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.customerPass.fields.customer_helper') }}</span>
</div>
<div class="form-group mb-3 col-6">
    <label class="form-label" for="pass_id">{{ trans('cruds.customerPass.fields.pass') }}</label>
    <select class="form-select form-select-sm {{ $errors->has('pass') ? 'is-invalid' : '' }}" name="pass_id" id="pass_id" data-control="select2">
        @foreach($passes as $id => $entry)
            <option value="{{ $id }}" {{ old('pass_id') == $id ? 'selected' : '' }}>{{ $entry }}</option>
        @endforeach
    </select>
    @if($errors->has('pass'))
        <div class="invalid-feedback">
            {{ $errors->first('pass') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.customerPass.fields.pass_helper') }}</span>
</div>
<div class="form-group mb-3 col-6">
    <label class="form-label" for="pass_code">{{ trans('cruds.customerPass.fields.pass_code') }}</label>
    <input class="form-control form-control-sm {{ $errors->has('pass_code') ? 'is-invalid' : '' }}" type="text" name="pass_code" id="pass_code" value="{{ old('pass_code', '') }}">
    @if($errors->has('pass_code'))
        <div class="invalid-feedback">
            {{ $errors->first('pass_code') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.customerPass.fields.pass_code_helper') }}</span>
</div>
<div class="form-group mb-3 col-6">
    <label class="form-label" for="price">{{ trans('cruds.customerPass.fields.price') }}</label>
    <input class="form-control form-control-sm {{ $errors->has('price') ? 'is-invalid' : '' }}" type="number" name="price" id="price" value="{{ old('price', '') }}" step="0.01">
    @if($errors->has('price'))
        <div class="invalid-feedback">
            {{ $errors->first('price') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.customerPass.fields.price_helper') }}</span>
</div>
<div class="form-group mb-3 col-6">
    <label class="form-label" for="start_date">{{ trans('cruds.customerPass.fields.start_date') }}</label>
    <input class="form-control form-control-sm date {{ $errors->has('start_date') ? 'is-invalid' : '' }}" type="text" name="start_date" id="start_date" value="{{ old('start_date') }}">
    @if($errors->has('start_date'))
        <div class="invalid-feedback">
            {{ $errors->first('start_date') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.customerPass.fields.start_date_helper') }}</span>
</div>
<div class="form-group mb-3 col-6">
    <label class="form-label" for="end_date">{{ trans('cruds.customerPass.fields.end_date') }}</label>
    <input class="form-control form-control-sm date {{ $errors->has('end_date') ? 'is-invalid' : '' }}" type="text" name="end_date" id="end_date" value="{{ old('end_date') }}">
    @if($errors->has('end_date'))
        <div class="invalid-feedback">
            {{ $errors->first('end_date') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.customerPass.fields.end_date_helper') }}</span>
</div>
<div class="form-group mb-3 col-6">
    <label class="form-label">{{ trans('cruds.customerPass.fields.status') }}</label>
    <select class="form-select form-select-sm {{ $errors->has('status') ? 'is-invalid' : '' }}" name="status" id="status" data-control="select2">
        <option value disabled {{ old('status', null) === null ? 'selected' : '' }}>{{ trans('global.pleaseSelect') }}</option>
        @foreach(App\Models\CustomerPass::STATUS_SELECT as $key => $label)
            <option value="{{ $key }}" {{ old('status', '') === (string) $key ? 'selected' : '' }}>{{ $label }}</option>
        @endforeach
    </select>
    @if($errors->has('status'))
        <div class="invalid-feedback">
            {{ $errors->first('status') }}
        </div>
    @endif
    <span class="help-block">{{ trans('cruds.customerPass.fields.status_helper') }}</span>
</div>