@extends('layouts.admin')

@section('header_title')
    {{ trans('global.create') }} {{ trans('cruds.pass.title_singular') }}
@endsection

@section('styles')
@endsection

@section('menu_wrapper')
    <div id="kt_app_toolbar" class="app-toolbar py-3 py-lg-6">
        <div id="kt_app_toolbar_container">
            <div class="page-title d-flex flex-column justify-content-center flex-wrap me-3">
                <h1 class="page-heading d-flex text-gray-900 fw-bold fs-3 flex-column justify-content-center my-0">
                    {{ trans('cruds.pass.title_singular') }}
                </h1>
                <ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0 pt-1">
                    <li class="breadcrumb-item text-muted">
                        <a href="/admin/passses" class="text-muted text-hover-primary">{{ trans('cruds.pass.title_singular') }}</a>
                    </li>
                    <li class="breadcrumb-item">
                        <span class="bullet bg-gray-500 w-5px h-2px"></span>
                    </li>
                    <li class="breadcrumb-item text-muted">{{ trans('global.create') }}
                        {{ trans('cruds.pass.title_singular') }}
                    </li>
                </ul>
            </div>
        </div>
    </div>
@endsection

@section('content')
    <div class="row g-0">
        <div class="col-lg-12 col-12 pe-lg-2">
            @include('layouts.common.tab_menu', [
                'tabs' => [
                    [
                        'option' => 1,
                        'name' => trans('global.create'),

                        'url' => route('admin.passes.create'),
                        'icon' => '',
                        'active' => Route::currentRouteName() == 'admin.passes.create' ? '1' : '0',
                    ],
                ],
            ])
            <div class="card custom-card mb-2 p-4 p-sm-5">

                <div class="card-body p-0">
                    <form class="row" method="POST" action="{{ route('admin.passes.store') }}" enctype="multipart/form-data">
                        @csrf
                        <div class="form-group mb-3 col-6">
                            <label class="form-label" for="title">{{ trans('cruds.pass.fields.title') }}</label>
                            <input class="form-control form-control-sm {{ $errors->has('title') ? 'is-invalid' : '' }}" type="text" name="title" id="title" value="{{ old('title', '') }}">
                            @if ($errors->has('title'))
                                <div class="invalid-feedback">
                                    {{ $errors->first('title') }}
                                </div>
                            @endif
                            <span class="help-block">{{ trans('cruds.pass.fields.title_helper') }}</span>
                        </div>
                        
                        <div class="form-group mb-3 col-6">
                            <label class="form-label" for="issuer_id">{{ trans('cruds.pass.fields.issuer') }}</label>
                            <select class="form-select form-select-sm {{ $errors->has('issuer') ? 'is-invalid' : '' }}" name="issuer_id" id="issuer_id" data-control="select2" {{ isset($issuer_id) ? 'disabled' : '' }}>
                                @foreach ($issuers as $id => $entry)
                                    <option value="{{ $id }}" {{ (old('issuer_id') ? old('issuer_id') : $issuer_id ?? '') == $id ? 'selected' : '' }}>{{ $entry }}</option>
                                @endforeach
                            </select>
                            @if (isset($issuer_id))
                                <input type="hidden" name="issuer_id" value="{{ $issuer_id }}">
                            @endif
                            @if ($errors->has('issuer'))
                                <div class="invalid-feedback">
                                    {{ $errors->first('issuer') }}
                                </div>
                            @endif
                            <span class="help-block">{{ trans('cruds.pass.fields.issuer_helper') }}</span>
                        </div>

                        <div class="form-group mb-3 col-3">
                            <label class="form-label" for="register_period_start_date">{{ trans('cruds.pass.fields.register_period_start_date') }}</label>
                            <input class="form-control form-control-sm date {{ $errors->has('register_period_start_date') ? 'is-invalid' : '' }}" type="text" name="register_period_start_date" id="register_period_start_date" value="{{ old('register_period_start_date') }}">
                            @if ($errors->has('register_period_start_date'))
                                <div class="invalid-feedback">
                                    {{ $errors->first('register_period_start_date') }}
                                </div>
                            @endif
                            <span class="help-block">{{ trans('cruds.pass.fields.register_period_start_date_helper') }}</span>
                        </div>
                        <div class="form-group mb-3 col-3">
                            <label class="form-label" for="register_period_end_date">{{ trans('cruds.pass.fields.register_period_end_date') }}</label>
                            <input class="form-control form-control-sm date {{ $errors->has('register_period_end_date') ? 'is-invalid' : '' }}" type="text" name="register_period_end_date" id="register_period_end_date" value="{{ old('register_period_end_date') }}">
                            @if ($errors->has('register_period_end_date'))
                                <div class="invalid-feedback">
                                    {{ $errors->first('register_period_end_date') }}
                                </div>
                            @endif
                            <span class="help-block">{{ trans('cruds.pass.fields.register_period_end_date_helper') }}</span>
                        </div>

                        <div class="form-group mb-3 col-3">
                            <label class="form-label" for="pass_status_id">{{ trans('cruds.pass.fields.pass_status') }}</label>
                            <div class="input-group input-group-solid flex-nowrap">
                                <div class="overflow-hidden flex-grow-1">
                                    <select class="form-select form-select-sm rounded-end-0 {{ $errors->has('pass_status_id') ? 'is-invalid' : '' }}" name="pass_status_id" id="pass_status_id" data-control="select2" data-allow-clear="true" data-placeholder="Please select...">
                                        @foreach ($pass_status as $id => $entry)
                                            <option value="{{ $id }}" {{ old('pass_status_id') == $id ? 'selected' : '' }}>{{ $entry }}</option>
                                        @endforeach
                                    </select>
                                </div>

                                @canany(['pass_status_create', 'pass_status_edit'])
                                    <span class="btn btn-sm btn-primary" id="drawer_pass_status" data-action="add" data-select="pass_status_id" data-target-id="">
                                        <i class="fa-solid fa-add"></i>
                                    </span>
                                @endcanany
                            </div>
                            @if ($errors->has('pass_status_id'))
                                <div class="invalid-feedback">
                                    {{ $errors->first('pass_status_id') }}
                                </div>
                            @endif
                            <span class="help-block">{{ trans('cruds.pass.fields.pass_status_helper') }}</span>
                        </div>

                        <div class="form-group mb-3 col-3">
                            <label class="form-label" for="pass_type_id">{{ trans('cruds.pass.fields.pass_type') }}</label>
                            <div class="input-group input-group-solid flex-nowrap">
                                <div class="overflow-hidden flex-grow-1">
                                    <select class="form-select form-select-sm rounded-end-0 {{ $errors->has('pass_type_id') ? 'is-invalid' : '' }}" name="pass_type_id" id="pass_type_id" data-control="select2" data-allow-clear="true" data-placeholder="Please select...">
                                        @foreach ($pass_types as $id => $entry)
                                            <option value="{{ $id }}" {{ old('pass_type_id') == $id ? 'selected' : '' }}>{{ $entry }}</option>
                                        @endforeach
                                    </select>
                                </div>
                                @canany(['pass_type_create', 'pass_type_edit'])
                                    <span class="btn btn-sm btn-primary" id="drawer_pass_type" data-action="add" data-select="pass_type_id" data-target-id="">
                                        <i class="fa-solid fa-add"></i>
                                    </span>
                                @endcanany
                            </div>
                            @if ($errors->has('pass_type_id'))
                                <div class="invalid-feedback">
                                    {{ $errors->first('pass_type_id') }}
                                </div>
                            @endif
                            <span class="help-block">{{ trans('cruds.pass.fields.pass_type_helper') }}</span>
                        </div>

                        <div class="form-group mb-3 col-12">
                            <label class="form-label" for="description">{{ trans('cruds.pass.fields.description') }}</label>
                            <textarea class="form-control  {{ $errors->has('description') ? 'is-invalid' : '' }}" name="description" id="description">{{ old('description') }}</textarea>
                            @if ($errors->has('description'))
                                <div class="invalid-feedback">
                                    {{ $errors->first('description') }}
                                </div>
                            @endif
                            <span class="help-block">{{ trans('cruds.pass.fields.description_helper') }}</span>
                        </div>

                        <div class="form-group mb-3">
                            <button class="btn btn-sm btn-danger" type="submit">
                                {{ trans('global.save') }}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <div id="kt_drawer" class="bg-white drawer_crud d-none" data-kt-drawer-activate="true" data-kt-drawer-close="#close_drawer" data-kt-drawer-overlay="true">
        <div class="card w-100" id="drawer_card">
        </div>
    </div>
@endsection

@section('scripts')
    <script src="{{ url('/') }}/assets/plugins/global/plugins.bundle.js"></script>
    <script src="{{ url('/') }}/assets/js/scripts.bundle.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.19.3/jquery.validate.min.js"></script>
    <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>
    <script src="{{ url('/') }}/assets/plugins/custom/ckeditor/ckeditor-classic.bundle.js"></script>
    <script src="{{ url('/') }}/assets/js/drawer.js"></script>

    <script>
        var csrfToken = "{{ csrf_token() }}";

        // Call function for PassStatus
        handleFormActions(
            'pass_status', // form id (any unique)
            'pass_status_id', // select id (from html)
            'drawer_pass_status', // drawer id (any unique)
            'PassStatus', // modelClass: model class name (from what model the data is fetch)
            'passStatuses', // crudFolderName: crud folder name ( same as view/admin )
            "{{ route('admin.pass-statuses.getForm') }}", // drawerFormUrl: url to get the form
            'passStatus', // crudName : use for lang (trans)
            'pass-statuses', // routeName : route from url
            @json(auth()->user()->can('pass_status_create')), // canCreate : permission
            @json(auth()->user()->can('pass_status_edit')), // canEdit : permission
            'id', // idField : option value
            'name', // displayField : option text
            'passStatus' // responseKey : reponse key from controller
        );

        // Call function for PassType
        handleFormActions(
            'pass_type', // form id (any unique)
            'pass_type_id', // select id (from html)
            'drawer_pass_type', // drawer id (any unique)
            'PassType', // modelClass: model class name (from what model the data is fetch)
            'passTypes', // crudFolderName: crud folder name ( same as view/admin )
            "{{ route('admin.pass-types.getForm') }}", // drawerFormUrl: url to get the form
            'passType', // crudName : use for lang (trans)
            'pass-types', // routeName : route from url
            @json(auth()->user()->can('pass_type_create')), // canCreate : permission
            @json(auth()->user()->can('pass_type_edit')), // canEdit : permission
            'id', // idField : option value
            'name', // displayField : option text
            'passType' // responseKey : reponse key from controller
        );

        $(document).ready(function() {
            let termAndConditionEditor;

            ClassicEditor
                .create(document.querySelector('#description'))
                .then(editor => {
                    termAndConditionEditor = editor;
                })
                .catch(error => {
                    console.error(error);
                });

            new tempusDominus.TempusDominus(document.getElementById("register_period_start_date"), {
                display: {
                    viewMode: "calendar",
                    components: {
                        decades: true,
                        year: true,
                        month: true,
                        date: true,
                        hours: false,
                        minutes: false,
                        seconds: false
                    }
                },
                localization: {
                    format: 'yyyy-MM-dd'
                }
            });
            new tempusDominus.TempusDominus(document.getElementById("register_period_end_date"), {
                display: {
                    viewMode: "calendar",
                    components: {
                        decades: true,
                        year: true,
                        month: true,
                        date: true,
                        hours: false,
                        minutes: false,
                        seconds: false
                    }
                },
                localization: {
                    format: 'yyyy-MM-dd'
                }
            });
        });
    </script>
@endsection
