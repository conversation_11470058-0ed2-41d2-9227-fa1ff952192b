@extends('layouts.admin')

@section('header_title')
    Dashboard
@endsection

@section('menu_wrapper')
    <div id="kt_app_toolbar" class="app-toolbar py-3 py-lg-6">
        <div id="kt_app_toolbar_container">
            <div class="page-title d-flex flex-column justify-content-center flex-wrap me-3">
                <h1 class="page-heading d-flex text-gray-900 fw-bold fs-3 flex-column justify-content-center my-0">
                    Dashboard
                </h1>
                <ul class="breadcrumb breadcrumb-separatorless fw-semibold fs-7 my-0 pt-1">
                    <li class="breadcrumb-item text-muted">
                        <a href="{{ route('admin.home') }}" class="text-muted text-hover-primary">
                            Dashboard
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </div>
@endsection

@section('styles')
<style>
    /* Base colors for light/dark mode compatibility */
    :root {
        --light-text: #ffffff;
        --dark-text: #181C32;
        --card-hover-shadow: rgba(59, 65, 94, 0.1);
        --primary-gradient: linear-gradient(135deg, #5e60ce, #673DE6);
        --success-gradient: linear-gradient(135deg, #0abb87, #06d6a0);
        --warning-gradient: linear-gradient(135deg, #ff9e0f, #ffbe0b);
        --info-gradient: linear-gradient(135deg, #0095e8, #48cae4);
        --danger-gradient: linear-gradient(135deg, #f72585, #b5179e);
        --dark-gradient: linear-gradient(135deg, #3a0ca3, #4361ee);
    }
    
    /* Animation Keyframes */
    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.03); }
        100% { transform: scale(1); }
    }
    
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    @keyframes gradientShift {
        0% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
        100% { background-position: 0% 50%; }
    }
    
    /* Global styles */
    .card {
        animation: fadeInUp 0.6s ease-out;
    }
    
    .dashboard-welcome {
        font-family: 'Poppins', sans-serif;
        font-weight: 700;
        font-size: 1.85rem;
        margin-bottom: 1.5rem;
        color: #ffffff;
        text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.15);
    }
    
    /* Stats Cards with Vibrant Colors */
    .stats-card {
        transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        border-radius: 1rem;
        overflow: hidden;
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.08);
        position: relative;
        border: none !important;
        backdrop-filter: blur(5px);
    }
    
    .stats-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.12);
    }
    
    .stats-card:active {
        transform: translateY(-5px);
    }
    
    .stats-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100%' height='100%' viewBox='0 0 800 800'%3E%3Cg %3E%3Ccircle fill='%23000000' cx='400' cy='400' r='600'/%3E%3Ccircle fill='%23180D1C' cx='400' cy='400' r='500'/%3E%3Ccircle fill='%2320101B' cx='400' cy='400' r='400'/%3E%3Ccircle fill='%2328141C' cx='400' cy='400' r='300'/%3E%3Ccircle fill='%2330191D' cx='400' cy='400' r='200'/%3E%3Ccircle fill='%23371D1F' cx='400' cy='400' r='100'/%3E%3C/g%3E%3C/svg%3E");
        opacity: 0.05;
        z-index: 0;
    }
    
    .stats-card .card-body {
        position: relative;
        z-index: 1;
    }
    
    .stats-icon {
        width: 65px;
        height: 65px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 1rem;
        font-size: 1.5rem;
        box-shadow: 0 8px 20px -5px rgba(0, 0, 0, 0.2);
        transform: translateZ(20px);
        transition: all 0.3s ease;
    }
    
    .stats-card:hover .stats-icon {
        transform: scale(1.1) translateZ(30px);
    }
    
    .stats-value {
        font-size: 2.25rem;
        font-weight: 700;
        font-family: 'Poppins', sans-serif;
        margin-top: 0.75rem;
        text-shadow: 0px 2px 4px rgba(0, 0, 0, 0.1);
        letter-spacing: -0.5px;
    }
    
    .stats-label {
        font-size: 1.1rem;
        font-weight: 500;
        font-family: 'Poppins', sans-serif;
        opacity: 0.9;
        letter-spacing: 0.5px;
    }
    
    /* Primary Stats Card */
    .stats-primary {
        background: var(--primary-gradient);
        background-size: 200% auto;
        animation: gradientShift 10s ease infinite;
    }
    .stats-primary .stats-icon {
        background-color: rgba(255, 255, 255, 0.2);
        color: #ffffff;
        text-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
    }
    .stats-primary .stats-value,
    .stats-primary .stats-label,
    .stats-primary .text-gray-400 {
        color: #ffffff !important;
    }
    
    /* Success Stats Card */
    .stats-success {
        background: var(--success-gradient);
        background-size: 200% auto;
        animation: gradientShift 10s ease infinite;
    }
    .stats-success .stats-icon {
        background-color: rgba(255, 255, 255, 0.2);
        color: #ffffff;
        text-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
    }
    .stats-success .stats-value,
    .stats-success .stats-label,
    .stats-success .text-gray-400 {
        color: #ffffff !important;
    }
    
    /* Warning Stats Card */
    .stats-warning {
        background: var(--warning-gradient);
        background-size: 200% auto;
        animation: gradientShift 10s ease infinite;
    }
    .stats-warning .stats-icon {
        background-color: rgba(255, 255, 255, 0.2);
        color: #ffffff;
        text-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
    }
    .stats-warning .stats-value,
    .stats-warning .stats-label,
    .stats-warning .text-gray-400 {
        color: #ffffff !important;
    }
    
    /* Info Stats Card */
    .stats-info {
        background: var(--info-gradient);
        background-size: 200% auto;
        animation: gradientShift 10s ease infinite;
    }
    .stats-info .stats-icon {
        background-color: rgba(255, 255, 255, 0.2);
        color: #ffffff;
        text-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
    }
    .stats-info .stats-value,
    .stats-info .stats-label,
    .stats-info .text-gray-400 {
        color: #ffffff !important;
    }
    
    /* Quick Action Cards */
    .quick-action-card {
        transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        cursor: pointer;
        border-radius: 1rem;
        height: 100%;
        position: relative;
        overflow: hidden;
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.08);
    }
    
    .quick-action-card:hover {
        transform: translateY(-10px) scale(1.03);
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.15);
    }
    
    .quick-action-card::after {
        content: '';
        position: absolute;
        width: 200%;
        height: 200%;
        border-radius: 50%;
        background: radial-gradient(circle, rgba(255,255,255,0.15) 0%, rgba(255,255,255,0) 70%);
        top: -50%;
        left: -50%;
        transition: all 0.5s ease;
    }
    
    .quick-action-card:hover::after {
        top: -80%;
        left: -30%;
    }
    
    .quick-action-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100%' height='100%' viewBox='0 0 800 800'%3E%3Cg %3E%3Ccircle fill='%23000000' cx='400' cy='400' r='600'/%3E%3Ccircle fill='%23180D1C' cx='400' cy='400' r='500'/%3E%3Ccircle fill='%2320101B' cx='400' cy='400' r='400'/%3E%3Ccircle fill='%2328141C' cx='400' cy='400' r='300'/%3E%3Ccircle fill='%2330191D' cx='400' cy='400' r='200'/%3E%3Ccircle fill='%23371D1F' cx='400' cy='400' r='100'/%3E%3C/g%3E%3C/svg%3E");
        opacity: 0.05;
        z-index: 0;
    }
    
    .quick-action-icon {
        font-size: 3rem;
        margin-bottom: 1.5rem;
        transition: all 0.3s ease;
        text-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        position: relative;
        z-index: 1;
    }
    
    .quick-action-card:hover .quick-action-icon {
        transform: scale(1.15);
        animation: pulse 1.5s infinite;
    }
    
    .quick-action-title {
        font-weight: 700;
        font-size: 1.3rem;
        font-family: 'Poppins', sans-serif;
        margin-bottom: 0.75rem;
        position: relative;
        z-index: 1;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    .quick-action-description {
        font-size: 1rem;
        font-family: 'Poppins', sans-serif;
        opacity: 0.9;
        position: relative;
        z-index: 1;
    }
    
    /* Vibrant backgrounds for quick action cards */
    .bg-vibrant-primary {
        background: var(--primary-gradient);
        background-size: 200% auto;
        animation: gradientShift 10s ease infinite;
        color: #ffffff !important;
    }
    
    .bg-vibrant-success {
        background: var(--success-gradient);
        background-size: 200% auto;
        animation: gradientShift 10s ease infinite;
        color: #ffffff !important;
    }
    
    .bg-vibrant-warning {
        background: var(--warning-gradient);
        background-size: 200% auto;
        animation: gradientShift 10s ease infinite;
        color: #ffffff !important;
    }
    
    .bg-vibrant-info {
        background: var(--info-gradient);
        background-size: 200% auto;
        animation: gradientShift 10s ease infinite;
        color: #ffffff !important;
    }
    
    .bg-vibrant-danger {
        background: var(--danger-gradient);
        background-size: 200% auto;
        animation: gradientShift 10s ease infinite;
        color: #ffffff !important;
    }
    
    .bg-vibrant-dark {
        background: var(--dark-gradient);
        background-size: 200% auto;
        animation: gradientShift 10s ease infinite;
        color: #ffffff !important;
    }
    
    /* Recent Activity */
    .recent-activity-item {
        padding: 1.2rem 0;
        border-bottom: 1px dashed #E4E6EF;
        position: relative;
        transition: all 0.3s ease;
    }
    
    .recent-activity-item:last-child {
        border-bottom: none;
    }
    
    .recent-activity-item:hover {
        background-color: rgba(245, 248, 250, 0.7);
        transform: translateX(5px);
        border-radius: 0.5rem;
        padding-left: 10px;
    }
    
    .bullet.bullet-vertical {
        width: 6px;
        height: 30px;
        border-radius: 6px;
        box-shadow: 0 3px 6px rgba(0, 0, 0, 0.08);
    }
    
    .activity-title {
        font-weight: 600;
        font-size: 1.1rem;
        font-family: 'Poppins', sans-serif;
    }
    
    .activity-message {
        color: var(--dark-text);
        font-family: 'Poppins', sans-serif;
        font-weight: 400;
    }
    
    .activity-date {
        font-size: 0.85rem;
        color: #B5B5C3;
        font-weight: 500;
    }
    
    /* Card headers */
    .card-header h3 {
        font-weight: 700;
        background: linear-gradient(135deg, #3a7bd5, #00d2ff, #6a11cb);
        background-size: 200% auto;
        animation: gradientShift 5s ease infinite;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        display: inline-block;
    }

    /* Welcome card gradient border */
    .welcome-card {
        border-radius: 1.5rem;
        position: relative;
        background: #fff;
        z-index: 1;
        overflow: hidden;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.07);
        transition: all 0.3s ease;
    }
    
    .welcome-card:hover {
        transform: translateY(-7px);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    }
    
    .welcome-card::before {
        content: '';
        position: absolute;
        inset: -3px;
        z-index: -1;
        border-radius: 1.6rem;
        background: linear-gradient(135deg, #5e60ce, #06d6a0, #ffbe0b, #f72585);
        background-size: 300% 300%;
        animation: gradientShift 10s ease infinite;
        opacity: 0.9;
    }
    
    /* Animation for page elements */
    .row {
        animation: fadeInUp 0.8s ease-out forwards;
        opacity: 0;
    }
    
    .row:nth-child(1) {
        animation-delay: 0.1s;
    }
    
    .row:nth-child(2) {
        animation-delay: 0.3s;
    }
    
    .row:nth-child(3) {
        animation-delay: 0.5s;
    }
    
    /* Dark mode compatibility */
    @media (prefers-color-scheme: dark) {
        .welcome-card {
            background: #1a1a2e;
        }
        
        .activity-message {
            color: #e0e0e0;
        }
        
        .recent-activity-item:hover {
            background-color: rgba(255, 255, 255, 0.05);
        }
        
        .text-gray-600 {
            color: #c8c8c8 !important;
        }
    }
    
    /* Glass effect for cards */
    .glass-effect {
        backdrop-filter: blur(10px);
        background-color: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }
</style>
@endsection

@section('content')
<div class="content">
    <!-- Welcome Section -->
    <div class="row mb-5">
        <div class="col-lg-12">
            <div class="card welcome-card">
                <div class="card-body p-lg-10">
                    @if(session('status'))
                        <div class="alert alert-success" role="alert">
                            {{ session('status') }}
                        </div>
                    @endif
                    
                    <div class="d-flex flex-column flex-lg-row">
                        <div class="flex-lg-row-fluid me-0 me-lg-20">
                            <div class="dashboard-welcome">Welcome to DoDo Pass Dashboard</div>
                            <p class="fs-6 fw-normal mb-7 text-white">
                                Your central hub for managing passes, packages, customers, and operations. 
                                Use the quick access tiles below to navigate to important areas.
                            </p>
                        </div>
                        <div class="d-flex justify-content-center justify-content-lg-end">
                            <img src="{{ url('/') }}/assets/img/logo_dodo_pass_46.png" class="h-125px" alt="DoDo Pass" />
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Stats -->
    <div class="row g-5 g-xl-8 mb-5">
        <!-- Passes -->
        <div class="col-xl-3 col-md-6">
            <div class="card stats-card stats-primary border-0 h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-2">
                        <div class="stats-icon me-3">
                            <i class="fa-solid fa-ticket"></i>
                        </div>
                        <span class="fw-semibold fs-6">Passes</span>
                    </div>
                    <div class="d-flex flex-column">
                        <span class="stats-value">586</span>
                        <span class="stats-label">Total Active Passes</span>
                    </div>
                </div>
            </div>
        </div>
        <!-- Customers -->
        <div class="col-xl-3 col-md-6">
            <div class="card stats-card stats-success border-0 h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-2">
                        <div class="stats-icon me-3">
                            <i class="fa-solid fa-users"></i>
                        </div>
                        <span class="fw-semibold fs-6">Customers</span>
                    </div>
                    <div class="d-flex flex-column">
                        <span class="stats-value">2,174</span>
                        <span class="stats-label">Registered Users</span>
                    </div>
                </div>
            </div>
        </div>
        <!-- Orders -->
        <div class="col-xl-3 col-md-6">
            <div class="card stats-card stats-warning border-0 h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-2">
                        <div class="stats-icon me-3">
                            <i class="fa-solid fa-shopping-cart"></i>
                        </div>
                        <span class="fw-semibold fs-6">Orders</span>
                    </div>
                    <div class="d-flex flex-column">
                        <span class="stats-value">425</span>
                        <span class="stats-label">This Month</span>
                    </div>
                </div>
            </div>
        </div>
        <!-- Revenue -->
        <div class="col-xl-3 col-md-6">
            <div class="card stats-card stats-info border-0 h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-2">
                        <div class="stats-icon me-3">
                            <i class="fa-solid fa-dollar-sign"></i>
                        </div>
                        <span class="fw-semibold fs-6">Revenue</span>
                    </div>
                    <div class="d-flex flex-column">
                        <span class="stats-value">$38,452</span>
                        <span class="stats-label">Monthly Revenue</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions & Recent Activity -->
    <div class="row g-5 g-xl-8 mb-5">
        <!-- Quick Actions -->
        <div class="col-xl-8">
            <div class="card h-100">
                <div class="card-header border-0">
                    <h3 class="card-title fw-bold">Quick Actions</h3>
                </div>
                <div class="card-body pt-0">
                    <div class="row g-4">
                        <div class="col-md-4 col-sm-6">
                            <div class="card quick-action-card bg-vibrant-primary border-0">
                                <div class="card-body text-center p-5">
                                    <i class="quick-action-icon fa-solid fa-plus"></i>
                                    <div class="quick-action-title mb-2">Create New Pass</div>
                                    <div class="quick-action-description">Add a new pass to the system</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 col-sm-6">
                            <div class="card quick-action-card bg-vibrant-success border-0">
                                <div class="card-body text-center p-5">
                                    <i class="quick-action-icon fa-solid fa-box"></i>
                                    <div class="quick-action-title mb-2">Manage Packages</div>
                                    <div class="quick-action-description">View and edit package details</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 col-sm-6">
                            <div class="card quick-action-card bg-vibrant-warning border-0">
                                <div class="card-body text-center p-5">
                                    <i class="quick-action-icon fa-solid fa-user-plus"></i>
                                    <div class="quick-action-title mb-2">Add Customer</div>
                                    <div class="quick-action-description">Register a new customer</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 col-sm-6">
                            <div class="card quick-action-card bg-vibrant-info border-0">
                                <div class="card-body text-center p-5">
                                    <i class="quick-action-icon fa-solid fa-chart-line"></i>
                                    <div class="quick-action-title mb-2">View Reports</div>
                                    <div class="quick-action-description">Access sales analytics</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 col-sm-6">
                            <div class="card quick-action-card bg-vibrant-danger border-0">
                                <div class="card-body text-center p-5">
                                    <i class="quick-action-icon fa-solid fa-cog"></i>
                                    <div class="quick-action-title mb-2">Settings</div>
                                    <div class="quick-action-description">Configure system settings</div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4 col-sm-6">
                            <div class="card quick-action-card bg-vibrant-dark border-0">
                                <div class="card-body text-center p-5">
                                    <i class="quick-action-icon fa-solid fa-question-circle"></i>
                                    <div class="quick-action-title mb-2">Help Center</div>
                                    <div class="quick-action-description">Get support and guidance</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Recent Activity -->
        <div class="col-xl-4">
            <div class="card h-100">
                <div class="card-header border-0">
                    <h3 class="card-title fw-bold">Recent Activity</h3>
                </div>
                <div class="card-body pt-0">
                    <div class="recent-activity-item">
                        <div class="d-flex align-items-center mb-2">
                            <div class="bullet bullet-vertical h-10px bg-primary me-3"></div>
                            <div class="fs-6 fw-semibold activity-title">New Pass Created</div>
                        </div>
                        <div class="d-flex">
                            <div class="ms-5 activity-message">Dubai Explorer Pass has been added to the system</div>
                            <div class="ms-auto activity-date">3 hrs ago</div>
                        </div>
                    </div>
                    <div class="recent-activity-item">
                        <div class="d-flex align-items-center mb-2">
                            <div class="bullet bullet-vertical h-10px bg-success me-3"></div>
                            <div class="fs-6 fw-semibold activity-title">Customer Registration</div>
                        </div>
                        <div class="d-flex">
                            <div class="ms-5 activity-message">New customer John Smith has registered</div>
                            <div class="ms-auto activity-date">5 hrs ago</div>
                        </div>
                    </div>
                    <div class="recent-activity-item">
                        <div class="d-flex align-items-center mb-2">
                            <div class="bullet bullet-vertical h-10px bg-warning me-3"></div>
                            <div class="fs-6 fw-semibold activity-title">Package Updated</div>
                        </div>
                        <div class="d-flex">
                            <div class="ms-5 activity-message">City Tour Package details have been modified</div>
                            <div class="ms-auto activity-date">8 hrs ago</div>
                        </div>
                    </div>
                    <div class="recent-activity-item">
                        <div class="d-flex align-items-center mb-2">
                            <div class="bullet bullet-vertical h-10px bg-info me-3"></div>
                            <div class="fs-6 fw-semibold activity-title">New Order</div>
                        </div>
                        <div class="d-flex">
                            <div class="ms-5 activity-message">Order #12345 has been placed for Dubai Pass</div>
                            <div class="ms-auto activity-date">1 day ago</div>
                        </div>
                    </div>
                    <div class="recent-activity-item">
                        <div class="d-flex align-items-center mb-2">
                            <div class="bullet bullet-vertical h-10px bg-danger me-3"></div>
                            <div class="fs-6 fw-semibold activity-title">Payment Received</div>
                        </div>
                        <div class="d-flex">
                            <div class="ms-5 activity-message">Payment of $1,250 received for order #12342</div>
                            <div class="ms-auto activity-date">2 days ago</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
@parent

@endsection