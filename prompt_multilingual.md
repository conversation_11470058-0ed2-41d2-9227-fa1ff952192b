# Multilingual Implementation Guide - SIMPLIFIED APPROACH

## Overview
This document outlines the **simplified** multilingual implementation using JSON columns and automatic locale detection. **NO FORM CHANGES NEEDED** - the trait handles everything automatically based on `app()->getLocale()`.

## Supported Languages
- English (en) - Primary/Fallback
- French (fr)
- German (de)

## How It Works
1. **Forms stay exactly the same** - no language tabs needed
2. **On save**: Trai<PERSON> detects `app()->getLocale()` and saves data to appropriate language in JSON
3. **On display**: Trait detects `app()->getLocale()` and returns appropriate translation, fallback to English

## Implementation Steps for Any Model

### 1. Database Migration
Create migration to add JSON translation columns:

```php
Schema::table('table_name', function (Blueprint $table) {
    $table->json('field_name_translations')->nullable()->after('field_name');
    // Repeat for each translatable field
});
```

### 2. Model Updates
Add Translatable trait and configure translatable fields:

```php
use App\Traits\Translatable;

class ModelName extends Model
{
    use Translatable;

    protected $translatable = [
        'field_name',
        'another_field',
    ];

    protected $fillable = [
        'field_name',
        'field_name_translations', // Add translation columns to fillable
        // ... other fields
    ];
}
```

### 3. Controller Updates
**NO CHANGES NEEDED** - Keep existing code:

```php
// This works automatically with the trait
$model = ModelName::create($request->all());
$model->update($request->all());
```

### 4. Form Updates
**NO CHANGES NEEDED** - Forms stay exactly the same

### 5. Validation Rules
**NO CHANGES NEEDED** - Keep existing validation

### 6. Display in Views
**NO CHANGES NEEDED** - Automatic translation:

```php
{{ $model->field_name }} // Automatically shows current locale
```

## Files Modified for ProviderCategory Implementation

### New Files Created:
1. `app/Traits/Translatable.php` - Reusable translation trait ✅ COMPLETED
2. `database/migrations/2025_05_28_134407_add_translations_to_provider_catergories_table.php` ✅ COMPLETED

### Modified Files:
1. `app/Models/ProviderCatergory.php` - Added Translatable trait and configuration ✅ COMPLETED

### Files NOT Modified (No Changes Needed):
- ❌ Controllers - Work automatically with existing code
- ❌ Validation Requests - Keep existing validation
- ❌ Views/Forms - Stay exactly the same
- ❌ JavaScript - No changes needed

## Key Features Implemented

### Translatable Trait Features:
- `getTranslation($field, $locale = null)` - Get translation for specific locale
- `setTranslation($field, $translations)` - Set translations for a field
- `hasTranslation($field, $locale)` - Check if translation exists
- Automatic accessor methods for translatable fields
- Fallback to English if translation missing
- Integration with current locale from header

### Form Features:
- Language tabs using Bootstrap
- Automatic current language detection from session
- Validation for all language fields
- Proper error handling per language

### Display Features:
- Automatic display in current user language
- Fallback to English if translation missing
- Consistent with existing language switching system

## Usage Examples

### Setting Translations:
```php
$category = new ProviderCatergory();
$category->setTranslation('name', [
    'en' => 'Category Name',
    'fr' => 'Nom de Catégorie',
    'de' => 'Kategoriename'
]);
```

### Getting Translations:
```php
$category->getTranslation('name'); // Current locale
$category->getTranslation('name', 'fr'); // Specific locale
$category->name; // Using accessor (current locale)
```

### In Blade Templates:
```php
{{ $category->name }} // Current locale
{{ $category->getTranslation('name', 'fr') }} // French
```

## Notes for Other Models

When implementing for other models:
1. Follow the same pattern as ProviderCategory
2. Update the `$translatable` array with your model's fields
3. Create similar migration for your table
4. Update controller, requests, and views following the same pattern
5. The Translatable trait is reusable across all models

## Language Integration

The implementation uses the existing language system:
- Reads current locale from `app()->getLocale()`
- Integrates with header language switcher
- Maintains session-based language preference
- Falls back to English (primary language) when translation missing

## Database Structure

Each translatable field gets a corresponding JSON column:
- `name` → `name_translations`
- `status` → `status_translations`
- `description` → `description_translations`

JSON structure: `{"en": "English text", "fr": "French text", "de": "German text"}`

## Implementation Status for ProviderCategory

✅ **COMPLETED FEATURES:**
- Translatable trait with full functionality
- Database migration with JSON translation columns
- Model updated with Translatable trait and configuration
- Controller updated to handle translation data
- Validation requests updated for all language fields
- Create form with language tabs and flag icons
- Edit form with language tabs and existing translation data
- Automatic language detection from header/session
- Fallback to English when translation missing
- Bootstrap tab interface with flag icons

🔧 **TECHNICAL DETAILS:**
- Uses existing language system (EN, FR, DE)
- Integrates with header language switcher
- JSON columns for efficient storage
- Backward compatibility maintained
- Validation for all language fields
- Error handling per language tab

📝 **USAGE AFTER IMPLEMENTATION:**
```php
// Creating a new category with translations
$category = new ProviderCatergory();
$category->setTranslation('name', [
    'en' => 'Tourism',
    'fr' => 'Tourisme',
    'de' => 'Tourismus'
]);
$category->save();

// Getting translations
echo $category->name; // Shows in current locale
echo $category->getTranslation('name', 'fr'); // Shows French version
```

## Real-World Usage Examples

### Example 1: User creates category in English
```php
// User switches to English in header
app()->setLocale('en');

// User submits form with name "Tourism"
$category = ProviderCatergory::create(['name' => 'Tourism']);
// Trait automatically saves: name_translations = {"en": "Tourism"}
```

### Example 2: User edits same category in French
```php
// User switches to French in header
app()->setLocale('fr');

// User edits same category with name "Tourisme"
$category->update(['name' => 'Tourisme']);
// Trait automatically updates: name_translations = {"en": "Tourism", "fr": "Tourisme"}
```

### Example 3: Displaying content
```php
// When user views in French
app()->setLocale('fr');
echo $category->name; // Shows "Tourisme"

// When user views in English
app()->setLocale('en');
echo $category->name; // Shows "Tourism"

// When user views in German (no translation exists)
app()->setLocale('de');
echo $category->name; // Shows "Tourism" (fallback to English)
```

## Implementation Status

✅ **COMPLETED FEATURES:**
- Automatic locale detection from `app()->getLocale()`
- Automatic translation saving on create/update
- Automatic translation retrieval on display
- Fallback to English when translation missing
- JSON storage for efficient database usage
- Backward compatibility with existing forms
- Zero form changes required
- Zero controller changes required

🎯 **READY FOR OTHER MODELS:**
The implementation is now complete and ready to be replicated for other models. Simply follow the same pattern using the Translatable trait.

## Quick Implementation for Other Models

For any other model, just:

1. **Create migration**: Add `field_name_translations` JSON columns
2. **Update model**: Add `use Translatable` and define `$translatable` array
3. **Done!** - Everything else works automatically

**Total files to modify per model: 2 files only (migration + model)**

## Multilingual Status Values Implementation

### Problem Solved:
How to make STATUS_SELECT values multilingual while using numeric keys instead of text.

### Solution:
1. **Updated STATUS_SELECT to use translation keys:**
```php
public const STATUS_SELECT = [
    '1' => 'global.active',
    '0' => 'global.inactive',
];
```

2. **Added translations to language files:**
```php
// resources/lang/en/global.php
'active' => 'Active',
'inactive' => 'Inactive',

// resources/lang/fr/global.php
'active' => 'Actif',
'inactive' => 'Inactif',

// resources/lang/de/global.php
'active' => 'Aktiv',
'inactive' => 'Inaktiv',
```

3. **Updated views to use trans() function:**
```php
@foreach(App\Models\ProviderCatergory::STATUS_SELECT as $key => $label)
    <option value="{{ $key }}">{{ trans($label) }}</option>
@endforeach
```

4. **Updated controller DataTable display:**
```php
$table->editColumn('status', function ($row) {
    if ($row->status && isset(ProviderCatergory::STATUS_SELECT[$row->status])) {
        return trans(ProviderCatergory::STATUS_SELECT[$row->status]);
    }
    return $row->status ? $row->status : '';
});
```

### Benefits:
- ✅ Status values are now fully multilingual
- ✅ Database stores numeric values (1/0) instead of text
- ✅ Display shows translated text based on current locale
- ✅ Forms show translated options
- ✅ DataTable displays translated status
- ✅ Consistent with existing translation system

### Usage:
```php
// Database stores: status = '1'
// Display shows:
// EN: "Active"
// FR: "Actif"
// DE: "Aktiv"
```

## Multilingual Pluck Operations

### Problem Solved:
How to make `pluck()` operations return translated values based on current locale.

### Solution:
Added `getTranslatedPluck()` method to Translatable trait that works with database queries.

### Usage:
```php
// Instead of:
$categories = ProviderCatergory::pluck('name', 'id');

// Use:
$categories = ProviderCatergory::getTranslatedPluck('name', 'id');
```

### How it works:
```php
// User has French locale
app()->setLocale('fr');
$categories = ProviderCatergory::getTranslatedPluck('name', 'id');
// Returns: [1 => "Catégorie Tourisme", 2 => "Sports d'Aventure"]

// User has German locale (with fallback)
app()->setLocale('de');
$categories = ProviderCatergory::getTranslatedPluck('name', 'id');
// Returns: [1 => "Tourism Category", 2 => "Abenteuersport"] // Falls back to English when German not available
```

### Implementation in Controllers:
```php
// Updated in ProviderController
$provider_catergories = ProviderCatergory::getTranslatedPluck('name', 'id')->prepend(trans('global.pleaseSelect'), '');
```

### Benefits:
- ✅ Works with existing `pluck()` usage patterns
- ✅ Automatic locale detection from `app()->getLocale()`
- ✅ Fallback to English when translation missing
- ✅ Database-level translation extraction for performance
- ✅ Compatible with `prepend()` and other collection methods
- ✅ No form or view changes needed

## Global Media Library Implementation

### Problem Solved:
Created a centralized media library system that can be used across the entire application instead of individual provider-specific media libraries.

### What was implemented:

#### 1. **New Controller**: `MediaLibraryController`
- Handles global media CRUD operations
- Upload, delete, search, and filter functionality
- Modal interface for media selection
- Latest banner retrieval API

#### 2. **New Routes**:
```php
Route::get('media-library', 'MediaLibraryController@index');
Route::post('media-library/upload', 'MediaLibraryController@upload');
Route::delete('media-library/{media}', 'MediaLibraryController@destroy');
Route::get('media-library/modal', 'MediaLibraryController@modal');
Route::get('media-library/latest-banner', 'MediaLibraryController@getLatestBanner');
```

#### 3. **New Views**:
- `admin/media-library/index.blade.php` - Main media library interface
- `admin/media-library/modal.blade.php` - Modal for media selection
- `admin/media-library/partials/media_items.blade.php` - Media grid items
- `admin/media-library/partials/media_pagination.blade.php` - Pagination

#### 4. **Navigation Integration**:
- Added "Media Library" to main sidebar navigation
- Accessible to users with provider_access permission

#### 5. **JavaScript Integration**: `public/js/global-media-library.js`
- Easy integration into existing forms
- Modal-based media selection
- jQuery plugin support
- Auto-initialization with data attributes

### Usage Examples:

#### Basic Integration:
```html
<!-- Add to any form -->
<input type="text" id="logo_url" name="logo_url" data-global-media="logo">
<!-- Button will be auto-created -->
```

#### jQuery Integration:
```javascript
$('#logo_url').globalMediaBrowser({
    imageType: 'logo',
    buttonText: 'Select Logo',
    showPreview: true
});
```

#### Programmatic Usage:
```javascript
const library = new GlobalMediaLibrary();
library.open((media) => {
    console.log('Selected media:', media);
    // Use media.url, media.name, media.id
}, 'banner');
```

#### Get Latest Banner:
```javascript
GlobalMediaLibrary.getLatestBanner((banner) => {
    if (banner) {
        document.getElementById('banner_url').value = banner.url;
    }
});
```

### Features:
- ✅ **Centralized Storage**: All media in one place
- ✅ **Image Type Filtering**: Logo, Banner, Slider, etc.
- ✅ **Search Functionality**: Search by name or filename
- ✅ **Upload Interface**: Drag & drop upload with validation
- ✅ **Modal Selection**: Clean modal interface for selection
- ✅ **Responsive Design**: Works on all screen sizes
- ✅ **Permission-Based**: Integrated with existing permission system
- ✅ **Easy Integration**: Simple JavaScript API for forms
- ✅ **Multilingual**: Supports EN, FR, DE languages

### Files Created:
1. `app/Http/Controllers/Admin/MediaLibraryController.php`
2. `resources/views/admin/media-library/index.blade.php`
3. `resources/views/admin/media-library/modal.blade.php`
4. `resources/views/admin/media-library/partials/media_items.blade.php`
5. `resources/views/admin/media-library/partials/media_pagination.blade.php`
6. `public/js/global-media-library.js`

### Files Modified:
1. `routes/web.php` - Added media library routes
2. `resources/views/partials/menu.blade.php` - Added navigation item
3. `resources/lang/*/global.php` - Added translation keys

### Benefits:
- ✅ **Reduces Duplication**: No more duplicate uploads across providers
- ✅ **Better Organization**: Centralized media management
- ✅ **Consistent Interface**: Same UI across all modules
- ✅ **Easy Maintenance**: Single place to manage all media
- ✅ **Reusable**: Can be used by providers, packages, passes, etc.
- ✅ **Performance**: Efficient database queries and caching
