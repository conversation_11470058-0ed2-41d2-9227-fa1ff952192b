<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Controllers\Traits\CsvImportTrait;
use App\Http\Controllers\Traits\MediaUploadingTrait;
use App\Http\Requests\MassDestroyProviderRequest;
use App\Http\Requests\StoreProviderRequest;
use App\Http\Requests\UpdateProviderRequest;
use App\Models\AgeGroup;
use App\Models\Country;
use App\Models\Provider;
use App\Models\ProviderCatergory;
use App\Models\ProviderLocation;
use App\Models\Market;
use App\Models\ProviderAgeGroup;
use App\Models\ProviderType;
use Gate;
use Illuminate\Http\Request;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use Symfony\Component\HttpFoundation\Response;
use Yajra\DataTables\Facades\DataTables;

class ProviderController extends Controller
{
    use MediaUploadingTrait, CsvImportTrait;

    public function index(Request $request)
    {
        abort_if(Gate::denies('provider_access'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        if ($request->ajax()) {
            $query = Provider::with(['phone_code', 'provider_catergory'])->select(sprintf('%s.*', (new Provider)->table));
            $table = Datatables::of($query);

            $table->addColumn('placeholder', '&nbsp;');
            $table->addColumn('actions', '&nbsp;');

            $table->editColumn('actions', function ($row) {
                $viewGate      = 'provider_show';
                $editGate      = 'provider_edit';
                $deleteGate    = 'provider_delete';
                $crudRoutePart = 'providers';

                return view('partials.datatablesActions', compact(
                    'viewGate',
                    'editGate',
                    'deleteGate',
                    'crudRoutePart',
                    'row'
                ));
            });

            $table->editColumn('id', function ($row) {
                $id = $row->id ? $row->id : '';
                $editLink = route('admin.providers.edit', $row->id);
                return $id ? '<div style="white-space: nowrap;">' . $id . ' - <a href="' . $editLink . '" class="no-highlight-edit badge badge-light-primary" onclick="event.stopPropagation();">Edit</a></div>' : '';
            });
            $table->editColumn('type', function ($row) {
                return $row->type ? Provider::TYPE_SELECT[$row->type] : '';
            });
            $table->editColumn('name', function ($row) {
                return $row->name ? $row->name : '';
            });
            $table->addColumn('phone_code_name', function ($row) {
                return $row->phone_code ? $row->phone_code->name : '';
            });

            $table->editColumn('phone', function ($row) {
                return $row->phone ? $row->phone : '';
            });
            $table->editColumn('email', function ($row) {
                return $row->email ? $row->email : '';
            });
            $table->editColumn('status', function ($row) {
                return $row->status ? Provider::STATUS_SELECT[$row->status] : '';
            });
            $table->editColumn('website_url', function ($row) {
                return $row->website_url ? $row->website_url : '';
            });
            $table->editColumn('vat_number', function ($row) {
                return $row->vat_number ? $row->vat_number : '';
            });
            $table->editColumn('logo', function ($row) {
                if ($photo = $row->logo) {
                    return sprintf(
                        '<a href="%s" target="_blank"><img src="%s" width="50px" height="50px"></a>',
                        $photo->url,
                        $photo->thumbnail
                    );
                }

                return '';
            });
            $table->addColumn('provider_catergory_name', function ($row) {
                return $row->provider_catergory ? $row->provider_catergory->name : '';
            });

            $table->addColumn('market_name', function ($row) {
                return $row->market ? $row->market->name : '';
            });

            $table->addColumn('provider_type_name', function ($row) {
                return $row->provider_type ? $row->provider_type->name : '';
            });

            $table->editColumn('slider', function ($row) {
                if (! $row->slider) {
                    return '';
                }
                $links = [];
                foreach ($row->slider as $media) {
                    $links[] = '<a href="' . $media->getUrl() . '" target="_blank"><img src="' . $media->getUrl('thumb') . '" width="50px" height="50px"></a>';
                }

                return implode(' ', $links);
            });

            $table->rawColumns(['actions', 'placeholder', 'phone_code', 'logo', 'provider_catergory', 'slider', 'id']);

            return $table->make(true);
        }

        return view('admin.providers.index');
    }

    public function create()
    {
        abort_if(Gate::denies('provider_create'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        $phone_codes = Country::select('id', 'phone_code', 'flag', 'name')->get();

        $provider_catergories = ProviderCatergory::getTranslatedPluck('name', 'id')->prepend(trans('global.pleaseSelect'), '');

        $provider_types = ProviderType::pluck('name', 'id')->prepend(trans('global.pleaseSelect'), '');

        $markets = Market::pluck('name', 'id')->prepend(trans('global.pleaseSelect'), '');

        return view('admin.providers.create', compact('markets', 'phone_codes', 'provider_catergories', 'provider_types'));
    }

    public function store(StoreProviderRequest $request)
    {
        $provider = Provider::create($request->all());

        // Handle logo URL directly from input field
        if ($request->filled('logo')) {
            $logoUrl = $request->input('logo');
            // Check if it's a URL from our media library
            if (filter_var($logoUrl, FILTER_VALIDATE_URL)) {
                // Find the media item by URL
                $mediaItem = Media::where('model_type', Provider::class)
                    ->where(function($query) use ($logoUrl) {
                        $query->where('original_url', $logoUrl)
                              ->orWhere('manipulations', 'like', '%' . $logoUrl . '%');
                    })
                    ->first();

                if ($mediaItem) {
                    // Associate this media with the provider
                    $mediaItem->model_id = $provider->id;
                    $mediaItem->collection_name = 'logo';
                    $mediaItem->save();
                }
            }
            // If it's a file upload from tmp directory
            elseif (file_exists(storage_path('tmp/uploads/' . basename($request->input('logo'))))) {
                $provider->addMedia(storage_path('tmp/uploads/' . basename($request->input('logo'))))
                    ->toMediaCollection('logo');
            }
        }

        foreach ($request->input('slider', []) as $file) {
            $provider->addMedia(storage_path('tmp/uploads/' . basename($file)))->toMediaCollection('slider');
        }

        if ($media = $request->input('ck-media', false)) {
            Media::whereIn('id', $media)->update(['model_id' => $provider->id]);
        }

        return redirect()->route('admin.providers.edit', $provider->id);
    }


    public function edit(Provider $provider)
    {
        abort_if(Gate::denies('provider_edit'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        $phone_codes = Country::select('id', 'phone_code', 'flag', 'name')->get();

        $provider_catergories = ProviderCatergory::getTranslatedPluck('name', 'id')->prepend(trans('global.pleaseSelect'), '');

        $markets = Market::pluck('name', 'id')->prepend(trans('global.pleaseSelect'), '');

        $provider->load('phone_code', 'provider_catergory', 'market');

        $provider_types = ProviderType::pluck('name', 'id')->prepend(trans('global.pleaseSelect'), '');

        return view('admin.providers.edit', compact('phone_codes', 'provider', 'provider_catergories', 'markets', 'provider_types'));
    }

    public function update(UpdateProviderRequest $request, Provider $provider)
    {
        $provider->update($request->all());

        // Handle logo URL directly from input field
        if ($request->filled('logo')) {
            $logoUrl = $request->input('logo');

            // Check if it's a URL from our media library
            if (filter_var($logoUrl, FILTER_VALIDATE_URL)) {
                // If the provider already has a logo and it's different from the current one
                if ($provider->logo && $provider->logo->getUrl() !== $logoUrl) {
                    // Delete the old logo
                    $provider->logo->delete();

                    // Find the media item by URL
                    $mediaItem = Media::where('model_type', Provider::class)
                        ->whereRaw("CONCAT(original_url) = ?", [$logoUrl])
                        ->orWhereRaw("JSON_CONTAINS(manipulations, ?, '$')", [$logoUrl])
                        ->first();

                    if ($mediaItem) {
                        // Associate this media with the provider
                        $mediaItem->model_id = $provider->id;
                        $mediaItem->collection_name = 'logo';
                        $mediaItem->save();
                    }
                }
            }
            // If it's a file upload from tmp directory
            elseif (file_exists(storage_path('tmp/uploads/' . basename($request->input('logo'))))) {
                if ($provider->logo) {
                    $provider->logo->delete();
                }
                $provider->addMedia(storage_path('tmp/uploads/' . basename($request->input('logo'))))
                    ->toMediaCollection('logo');
            }
        } elseif ($provider->logo) {
            $provider->logo->delete();
        }

        if (count($provider->slider) > 0) {
            foreach ($provider->slider as $media) {
                if (! in_array($media->file_name, $request->input('slider', []))) {
                    $media->delete();
                }
            }
        }
        $media = $provider->slider->pluck('file_name')->toArray();
        foreach ($request->input('slider', []) as $file) {
            if (count($media) === 0 || ! in_array($file, $media)) {
                $provider->addMedia(storage_path('tmp/uploads/' . basename($file)))->toMediaCollection('slider');
            }
        }

        return redirect()->back();
    }


    public function show(Provider $provider)
    {
        abort_if(Gate::denies('provider_show'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        $provider->load('phone_code', 'provider_catergory', 'providerPackages', 'providerProviderLocations');

        return view('admin.providers.show', compact('provider'));
    }

    public function destroy(Provider $provider)
    {
        abort_if(Gate::denies('provider_delete'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        $provider->delete();

        return back();
    }

    public function massDestroy(MassDestroyProviderRequest $request)
    {
        $providers = Provider::find(request('ids'));

        foreach ($providers as $provider) {
            $provider->delete();
        }

        return response(null, Response::HTTP_NO_CONTENT);
    }

    public function storeCKEditorImages(Request $request)
    {
        abort_if(Gate::denies('provider_create') && Gate::denies('provider_edit'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        $model         = new Provider();
        $model->id     = $request->input('crud_id', 0);
        $model->exists = true;
        $media         = $model->addMediaFromRequest('upload')->toMediaCollection('ck-media');

        return response()->json(['id' => $media->id, 'url' => $media->getUrl()], Response::HTTP_CREATED);
    }

    public function providerLocation(Provider $provider)
    {
        abort_if(Gate::denies('provider_edit'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        return view('admin.providers.location', compact('provider'));
    }


    public function locationAddForm(Request $request)
    {
        if ($request->ajax()) {
            $data = $request->all();
            $modal_type = $data['modal_type'];
            $related_id = $data['related_id'];

            $countries = Country::select('id', 'phone_code', 'flag', 'name')->get();

            $modal_content = view('admin.providers.addLocationForm', compact(

                'countries',
                'related_id'
            ))->render();

            return response()->json([
                'success' => 'Status updated successfully.',
                'modal_type' => $modal_type,
                'modal_content' => $modal_content
            ]);
        } else {
            return response()->json(['error' => 'Error updating status.']);
        }
    }

    public function locationEditForm(Request $request)
    {
        if ($request->ajax()) {
            $data = $request->all();
            $modal_type = $data['modal_type'];
            $related_id = $data['related_id'];
            $entity_id = $data['entity_id'];

            $countries = Country::select('id', 'phone_code', 'flag', 'name')->get();
            $entity = ProviderLocation::find($entity_id);

            $modal_content = view('admin.providers.editLocationForm', compact(
                'modal_type',
                'countries',
                'related_id',
                'entity'
            ))->render();

            return response()->json([
                'success' => 'Status updated successfully.',
                'modal_type' => $modal_type,
                'modal_content' => $modal_content
            ]);
        } else {
            return response()->json(['error' => 'Error updating status.']);
        }
    }

    public function providerAgeGroup(Provider $provider)
    {
        abort_if(Gate::denies('provider_edit'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        return view('admin.providers.ageGroup', compact('provider'));
    }

    public function providerSocial(Provider $provider)
    {
        abort_if(Gate::denies('provider_edit'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        return view('admin.providers.social', compact('provider'));
    }

    public function ageGroupEditForm(Request $request)
    {
        if ($request->ajax()) {
            $data = $request->all();
            $modal_type = $data['modal_type'];
            $related_id = $data['related_id'];
            $entity_id = $data['entity_id'];

            $countries = Country::pluck('name', 'id')->prepend(trans('global.pleaseSelect'), '');
            $entity = ProviderAgeGroup::find($entity_id);

            $modal_content = view('admin.providers.editAgeGroupForm', compact(
                'modal_type',
                'countries',
                'related_id',
                'entity'
            ))->render();

            return response()->json([
                'success' => 'Status updated successfully.',
                'modal_type' => $modal_type,
                'modal_content' => $modal_content
            ]);
        } else {
            return response()->json(['error' => 'Error updating status.']);
        }
    }

    public function mediaLibrary(Provider $provider,Request $request)
    {
        abort_if(Gate::denies('provider_edit'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        $mediaQuery = Media::query()->where('model_type', Provider::class)->where('model_id', $provider->id);

        $search = $request->get('search');
        if (!empty($search)) {
            $mediaQuery->where(function($query) use ($search) {
                $query->where('name', 'like', "%{$search}%")
                      ->orWhere('image_type', 'like', "%{$search}%");
            });
        }

        $media = $mediaQuery->orderBy('id', 'desc')->paginate(8);

        $media->appends(['search' => $search]);

        // If this is an AJAX request, return JSON with HTML content
        if ($request->ajax() || $request->get('ajax')) {
            $html = view('admin.providers.partials.media_items', compact('media'))->render();
            $pagination = view('admin.providers.partials.media_pagination', compact('media'))->render();

            return response()->json([
                'html' => $html,
                'pagination' => $pagination
            ]);
        }

        return view('admin.providers.media_library', compact('provider', 'media', 'search'));
    }

    public function mediaLibraryUpload(Provider $provider, Request $request)
    {
        $request->validate([
            'file' => 'required|image|max:20480',
            'image_type' => 'required|string|in:' . implode(',', array_keys(Provider::IMAGE_TYPES))
        ]);

        // Determine the collection based on image type
        $collection = 'photos';
        if ($request->input('image_type') === 'logo') {
            $collection = 'logo';
        } elseif ($request->input('image_type') === 'slider') {
            $collection = 'slider';
        }

        $media = $provider->addMedia($request->file('file'))->toMediaCollection($collection);

        // Update the image_type field in the media record
        $media->image_type = $request->input('image_type');
        $media->save();

        return response()->json([
            'status' => 'success',
            'message' => 'Image uploaded',
            'image_type' => $request->input('image_type'),
            'url' => $media->getUrl(),
            'media' => $media
        ]);
    }

    public function providerPackages(Provider $provider)
    {
        abort_if(Gate::denies('provider_edit'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        $packages = $provider->providerPackages;

        // Eager load the media for each package
        foreach ($packages as $package) {
            $package->load('media');
        }

        return view('admin.providers.packages', compact('provider', 'packages'));
    }

    public function providerBanks(Provider $provider)
    {
        abort_if(Gate::denies('provider_edit'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        $providerBanks = $provider->providerBanks()->with('bank')->get();
        $banks = \App\Models\Bank::where('status', 'active')->pluck('name', 'id')->prepend(trans('global.pleaseSelect'), '');

        return view('admin.providers.banks', compact('provider', 'providerBanks', 'banks'));
    }

    public function bankAddForm(Request $request)
    {
        if ($request->ajax()) {
            $data = $request->all();
            $related_id = $data['related_id'];

            $banks = \App\Models\Bank::where('status', 'active')->pluck('name', 'id')->prepend(trans('global.pleaseSelect'), '');

            $modal_content = view('admin.providers.addBankForm', compact(
                'banks',
                'related_id'
            ))->render();

            return response()->json([
                'success' => 'Status updated successfully.',
                'modal_content' => $modal_content
            ]);
        } else {
            return response()->json(['error' => 'Error updating status.']);
        }
    }

    public function bankEditForm(Request $request)
    {
        if ($request->ajax()) {
            $data = $request->all();
            $related_id = $data['related_id'];
            $entity_id = $data['entity_id'];

            $banks = \App\Models\Bank::where('status', 'active')->pluck('name', 'id')->prepend(trans('global.pleaseSelect'), '');
            $entity = \App\Models\ProviderBank::find($entity_id);

            $modal_content = view('admin.providers.editBankForm', compact(
                'banks',
                'related_id',
                'entity'
            ))->render();

            return response()->json([
                'success' => 'Status updated successfully.',
                'modal_content' => $modal_content
            ]);
        } else {
            return response()->json(['error' => 'Error updating status.']);
        }
    }

    public function providerBilling(Provider $provider)
    {
        abort_if(Gate::denies('provider_edit'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        return view('admin.providers.billing', compact('provider'));
    }

    public function updateBilling(Request $request, Provider $provider)
    {
        abort_if(Gate::denies('provider_edit'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        $provider->update($request->all());

        return redirect()->route('admin.providers.billing', $provider->id)->with('message', 'Billing information updated successfully');
    }
}
