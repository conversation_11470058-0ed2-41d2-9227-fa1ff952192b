<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Controllers\Traits\CsvImportTrait;
use App\Http\Controllers\Traits\DrawerFormTrait;
use App\Http\Requests\MassDestroyProviderCatergoryRequest;
use App\Http\Requests\StoreProviderCatergoryRequest;
use App\Http\Requests\UpdateProviderCatergoryRequest;
use App\Models\ProviderCatergory;
use Gate;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Yajra\DataTables\Facades\DataTables;

class ProviderCatergoryController extends Controller
{
    use CsvImportTrait,DrawerFormTrait;

    public function index(Request $request)
    {
        abort_if(Gate::denies('provider_catergory_access'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        if ($request->ajax()) {
            $query = ProviderCatergory::query()->select(sprintf('%s.*', (new ProviderCatergory)->table));
            $table = Datatables::of($query);

            $table->addColumn('placeholder', '&nbsp;');
            $table->addColumn('actions', '&nbsp;');

            $table->editColumn('actions', function ($row) {
                $viewGate      = 'provider_catergory_show';
                $editGate      = 'provider_catergory_edit';
                $deleteGate    = 'provider_catergory_delete';
                $crudRoutePart = 'provider-catergories';

                return view('partials.datatablesActions', compact(
                    'viewGate',
                    'editGate',
                    'deleteGate',
                    'crudRoutePart',
                    'row'
                ));
            });

            $table->editColumn('id', function ($row) {
                $id = $row->id ? $row->id : '';
                $editLink = route('admin.provider-catergories.edit', $row->id);
                return $id ? $id . ' - <a href="' . $editLink . '" class="no-highlight-edit badge badge-light-primary" onclick="event.stopPropagation();">Edit</a>' : '';
            });
            $table->editColumn('name', function ($row) {
                return $row->name ? $row->name : '';
            });
            $table->editColumn('status', function ($row) {
                if ($row->status && isset(ProviderCatergory::STATUS_SELECT[$row->status])) {
                    return trans(ProviderCatergory::STATUS_SELECT[$row->status]);
                }
                return $row->status ? $row->status : '';
            });
            $table->editColumn('description', function ($row) {
                return $row->description ? $row->description : '';
            });

            $table->rawColumns(['actions', 'placeholder', 'id']);

            return $table->make(true);
        }

        return view('admin.providerCatergories.index');
    }

    public function create()
    {
        abort_if(Gate::denies('provider_catergory_create'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        return view('admin.providerCatergories.create');
    }

    public function store(StoreProviderCatergoryRequest $request)
    {
        if ($request->ajax()) {

            $providerCatergory = ProviderCatergory::create($request->all());

            return response()->json([
                'success' => 'Status updated successfully.',
                'providerCatergory' => $providerCatergory
            ]);
        }

        $providerCatergory = ProviderCatergory::create($request->all());

        return redirect()->route('admin.provider-catergories.index');
    }

    public function edit(ProviderCatergory $providerCatergory)
    {
        abort_if(Gate::denies('provider_catergory_edit'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        return view('admin.providerCatergories.edit', compact('providerCatergory'));
    }

    public function update(UpdateProviderCatergoryRequest $request, ProviderCatergory $providerCatergory)
    {

        if ($request->ajax()) {

            $providerCatergory->update($request->all());

            return response()->json([
                'success' => 'Status updated successfully.',
                'providerCatergory' => $providerCatergory
            ]);
        }

        $providerCatergory->update($request->all());

        return redirect()->route('admin.provider-catergories.index');
    }

    public function show(ProviderCatergory $providerCatergory)
    {
        abort_if(Gate::denies('provider_catergory_show'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        $providerCatergory->load('providerCatergoryProviders');

        return view('admin.providerCatergories.show', compact('providerCatergory'));
    }

    public function destroy(ProviderCatergory $providerCatergory)
    {
        abort_if(Gate::denies('provider_catergory_delete'), Response::HTTP_FORBIDDEN, '403 Forbidden');

        $providerCatergory->delete();

        return back();
    }

    public function massDestroy(MassDestroyProviderCatergoryRequest $request)
    {
        $providerCatergories = ProviderCatergory::find(request('ids'));

        foreach ($providerCatergories as $providerCatergory) {
            $providerCatergory->delete();
        }

        return response(null, Response::HTTP_NO_CONTENT);
    }
}
