<?php

namespace App\Models;

use App\Traits\Auditable;
use App\Traits\Translatable;
use DateTimeInterface;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class ProviderCatergory extends Model
{
    use SoftDeletes, Auditable, HasFactory, Translatable;

    public $table = 'provider_catergories';

    protected $dates = [
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    public const STATUS_SELECT = [
        '1' => 'global.active',
        '0' => 'global.inactive',
    ];

    /**
     * Fields that are translatable
     */
    protected $translatable = [
        'name',
        'status',
        'description',
    ];

    protected $fillable = [
        'name',
        'status',
        'description',
        'name_translations',
        'status_translations',
        'description_translations',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    protected function serializeDate(DateTimeInterface $date)
    {
        return $date->format('Y-m-d H:i:s');
    }

    public function providerCatergoryProviders()
    {
        return $this->hasMany(Provider::class, 'provider_catergory_id', 'id');
    }
}
