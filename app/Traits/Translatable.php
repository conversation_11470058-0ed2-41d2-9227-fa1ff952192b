<?php

namespace App\Traits;

use Illuminate\Support\Facades\App;

trait Translatable
{
    /**
     * Get the available languages from config
     */
    public function getAvailableLanguages(): array
    {
        return array_keys(config('panel.available_languages', ['en' => 'English']));
    }

    /**
     * Get the primary language (fallback)
     */
    public function getPrimaryLanguage(): string
    {
        return config('panel.primary_language', 'en');
    }

    /**
     * Get translatable fields for this model
     */
    public function getTranslatableFields(): array
    {
        return $this->translatable ?? [];
    }

    /**
     * Check if field is translatable
     */
    public function isTranslatableField(string $field): bool
    {
        return in_array($field, $this->getTranslatableFields());
    }

    /**
     * Get translation field name
     */
    public function getTranslationFieldName(string $field): string
    {
        return $field . '_translations';
    }

    /**
     * Override getAttribute to automatically return translated content
     */
    public function getAttribute($key)
    {
        // Check if this is a translatable field
        if ($this->isTranslatableField($key)) {
            return $this->getTranslatedValue($key);
        }

        return parent::getAttribute($key);
    }

    /**
     * Override setAttribute to automatically save translations
     */
    public function setAttribute($key, $value)
    {
        // Check if this is a translatable field
        if ($this->isTranslatableField($key)) {
            $this->setTranslatedValue($key, $value);
            return $this;
        }

        return parent::setAttribute($key, $value);
    }

    /**
     * Get translated value for current locale
     */
    protected function getTranslatedValue(string $field): ?string
    {
        $currentLocale = App::getLocale();

        // Get existing translations
        $translations = $this->getTranslations($field);

        // Return translation for current locale if exists
        if (isset($translations[$currentLocale]) && !empty($translations[$currentLocale])) {
            return $translations[$currentLocale];
        }

        // Fallback to primary language
        $primaryLanguage = $this->getPrimaryLanguage();
        if (isset($translations[$primaryLanguage]) && !empty($translations[$primaryLanguage])) {
            return $translations[$primaryLanguage];
        }

        // Fallback to original field value if no translations exist
        return parent::getAttribute($field);
    }

    /**
     * Set translated value for current locale
     */
    protected function setTranslatedValue(string $field, $value): void
    {
        $currentLocale = App::getLocale();
        $translationField = $this->getTranslationFieldName($field);

        // Get existing translations
        $translations = $this->getTranslations($field);

        // Update translation for current locale
        $translations[$currentLocale] = $value;

        // Save back to translation field
        parent::setAttribute($translationField, json_encode($translations));

        // Also update the original field for backward compatibility
        parent::setAttribute($field, $value);
    }

    /**
     * Get all translations for a field
     */
    public function getTranslations(string $field): array
    {
        $translationField = $this->getTranslationFieldName($field);
        $translations = parent::getAttribute($translationField);

        if (is_string($translations)) {
            $translations = json_decode($translations, true) ?: [];
        }

        return is_array($translations) ? $translations : [];
    }

    /**
     * Get translation for specific locale
     */
    public function getTranslation(string $field, ?string $locale = null): ?string
    {
        $locale = $locale ?: App::getLocale();
        $translations = $this->getTranslations($field);

        // Return translation for requested locale if exists
        if (isset($translations[$locale]) && !empty($translations[$locale])) {
            return $translations[$locale];
        }

        // Fallback to primary language
        $primaryLanguage = $this->getPrimaryLanguage();
        if (isset($translations[$primaryLanguage]) && !empty($translations[$primaryLanguage])) {
            return $translations[$primaryLanguage];
        }

        // Fallback to original field
        return parent::getAttribute($field);
    }

    /**
     * Set translation for specific locale
     */
    public function setTranslation(string $field, string $locale, $value): self
    {
        if (!$this->isTranslatableField($field)) {
            return $this;
        }

        $translationField = $this->getTranslationFieldName($field);
        $translations = $this->getTranslations($field);

        $translations[$locale] = $value;

        parent::setAttribute($translationField, json_encode($translations));

        return $this;
    }

    /**
     * Check if translation exists for a field and locale
     */
    public function hasTranslation(string $field, string $locale): bool
    {
        $translations = $this->getTranslations($field);
        return isset($translations[$locale]) && !empty($translations[$locale]);
    }

    /**
     * Scope to select translated fields for current locale
     */
    public function scopeWithTranslations($query)
    {
        $currentLocale = App::getLocale();
        $primaryLanguage = $this->getPrimaryLanguage();

        foreach ($this->getTranslatableFields() as $field) {
            $translationField = $this->getTranslationFieldName($field);

            // Add a computed column that returns the translated value
            $query->selectRaw("
                CASE
                    WHEN JSON_EXTRACT({$translationField}, '$.{$currentLocale}') IS NOT NULL
                         AND JSON_EXTRACT({$translationField}, '$.{$currentLocale}') != ''
                    THEN JSON_UNQUOTE(JSON_EXTRACT({$translationField}, '$.{$currentLocale}'))
                    WHEN JSON_EXTRACT({$translationField}, '$.{$primaryLanguage}') IS NOT NULL
                         AND JSON_EXTRACT({$translationField}, '$.{$primaryLanguage}') != ''
                    THEN JSON_UNQUOTE(JSON_EXTRACT({$translationField}, '$.{$primaryLanguage}'))
                    ELSE {$field}
                END as {$field}_translated
            ");
        }

        return $query;
    }

    /**
     * Get a collection with translated values for pluck operations
     */
    public static function getTranslatedPluck($valueField, $keyField = 'id')
    {
        $instance = new static();

        if (!$instance->isTranslatableField($valueField)) {
            // If field is not translatable, use regular pluck
            return static::pluck($valueField, $keyField);
        }

        $currentLocale = App::getLocale();
        $primaryLanguage = $instance->getPrimaryLanguage();
        $translationField = $instance->getTranslationFieldName($valueField);

        return static::selectRaw("
            {$keyField},
            CASE
                WHEN JSON_EXTRACT({$translationField}, '$.{$currentLocale}') IS NOT NULL
                     AND JSON_EXTRACT({$translationField}, '$.{$currentLocale}') != ''
                THEN JSON_UNQUOTE(JSON_EXTRACT({$translationField}, '$.{$currentLocale}'))
                WHEN JSON_EXTRACT({$translationField}, '$.{$primaryLanguage}') IS NOT NULL
                     AND JSON_EXTRACT({$translationField}, '$.{$primaryLanguage}') != ''
                THEN JSON_UNQUOTE(JSON_EXTRACT({$translationField}, '$.{$primaryLanguage}'))
                ELSE {$valueField}
            END as translated_value
        ")->pluck('translated_value', $keyField);
    }

    /**
     * Boot the trait
     */
    public static function bootTranslatable()
    {
        // Any boot logic can be added here if needed
    }
}
